//! Configuration system for the templating engine

use serde::{Deserialize, Serialize};
use std::time::Duration;

pub mod builder;
pub mod defaults;

/// Main engine configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineConfig {
    pub memory_limit: usize,
    pub batch_size: usize,
    pub delimiters: DelimiterConfig,
    pub syntax: SyntaxConfig,
    pub error_handling: ErrorHandlingConfig,
    pub performance: PerformanceConfig,
}

/// Delimiter configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DelimiterConfig {
    pub start: String,
    pub end: String,
    pub change_prefix: Option<String>,
}

/// Syntax configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SyntaxConfig {
    pub loop_start_prefix: String,
    pub loop_end_prefix: String,
    pub condition_prefix: String,
    pub raw_xml_prefix: String,
    pub case_sensitive: bool,
}

/// Error handling configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ErrorHandlingConfig {
    pub fail_fast: bool,
    pub collect_all_errors: bool,
    pub max_errors: usize,
    pub include_context: bool,
}

/// Performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub regex_cache_size: usize,
    pub buffer_pool_size: usize,
    pub max_concurrent_tasks: usize,
    pub io_timeout: Duration,
    pub backpressure_threshold: f64,
}

/// Configuration builder with validation
pub struct EngineConfigBuilder {
    memory_limit: usize,
    batch_size: usize,
    delimiters: DelimiterConfig,
    syntax: SyntaxConfig,
    error_handling: ErrorHandlingConfig,
    performance: PerformanceConfig,
}

impl Default for EngineConfig {
    fn default() -> Self {
        Self {
            memory_limit: 64 * 1024 * 1024, // 64MB
            batch_size: 1000,
            delimiters: DelimiterConfig::default(),
            syntax: SyntaxConfig::default(),
            error_handling: ErrorHandlingConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl Default for DelimiterConfig {
    fn default() -> Self {
        Self {
            start: "{".to_string(),
            end: "}".to_string(),
            change_prefix: Some("=".to_string()),
        }
    }
}

impl Default for SyntaxConfig {
    fn default() -> Self {
        Self {
            loop_start_prefix: "#".to_string(),
            loop_end_prefix: "/".to_string(),
            condition_prefix: "?".to_string(),
            raw_xml_prefix: "@".to_string(),
            case_sensitive: false,
        }
    }
}

impl Default for ErrorHandlingConfig {
    fn default() -> Self {
        Self {
            fail_fast: false,
            collect_all_errors: true,
            max_errors: 100,
            include_context: true,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            regex_cache_size: 100,
            buffer_pool_size: 50,
            max_concurrent_tasks: num_cpus(),
            io_timeout: Duration::from_secs(30),
            backpressure_threshold: 0.8,
        }
    }
}

impl EngineConfig {
    /// Create a new configuration builder
    pub fn builder() -> EngineConfigBuilder {
        EngineConfigBuilder::new()
    }
}

impl EngineConfigBuilder {
    pub fn new() -> Self {
        Self {
            memory_limit: 64 * 1024 * 1024,
            batch_size: 1000,
            delimiters: DelimiterConfig::default(),
            syntax: SyntaxConfig::default(),
            error_handling: ErrorHandlingConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl Default for EngineConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

// Helper function to get number of CPUs
fn num_cpus() -> usize {
    std::thread::available_parallelism()
        .map(|n| n.get())
        .unwrap_or(4)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config() {
        let config = EngineConfig::default();
        assert_eq!(config.memory_limit, 64 * 1024 * 1024);
        assert_eq!(config.batch_size, 1000);
        assert_eq!(config.delimiters.start, "{");
        assert_eq!(config.delimiters.end, "}");
    }
    
    #[test]
    fn test_config_builder() {
        let config = EngineConfigBuilder::new()
            .memory_limit(32 * 1024 * 1024)
            .batch_size(500)
            .delimiters("{{", "}}")
            .build()
            .unwrap();
        
        assert_eq!(config.memory_limit, 32 * 1024 * 1024);
        assert_eq!(config.batch_size, 500);
        assert_eq!(config.delimiters.start, "{{");
        assert_eq!(config.delimiters.end, "}}");
    }
    
    #[test]
    fn test_config_validation() {
        let result = EngineConfigBuilder::new()
            .memory_limit(0)
            .build();
        
        assert!(result.is_err());
    }
}
