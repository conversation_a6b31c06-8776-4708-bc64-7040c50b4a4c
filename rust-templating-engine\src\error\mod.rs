//! Error handling for the templating engine
//!
//! This module provides a comprehensive error hierarchy using `thiserror` for
//! better error messages and context.

pub mod types;
pub mod context;
pub mod conversion;

// Re-export main types
pub use types::*;
pub use context::{Position, ErrorContext, <PERSON><PERSON><PERSON><PERSON>everity, ErrorInfo};
pub use conversion::{ErrorContextExt, ErrorCollector, helpers};

/// Result type alias for convenience
pub type Result<T> = std::result::Result<T, TemplateError>;

/// Document loading result type
pub type DocumentResult<T> = std::result::Result<T, DocumentLoadError>;

/// Template parsing result type  
pub type ParseResult<T> = std::result::Result<T, TemplateParseError>;

/// Rendering result type
pub type RenderResult<T> = std::result::Result<T, RenderError>;

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_error_display() {
        let error = TemplateError::InvalidFormat {
            message: "Test error".to_string(),
        };
        
        let display = format!("{}", error);
        assert!(display.contains("Test error"));
    }
    
    #[test]
    fn test_error_debug() {
        let error = TemplateError::InvalidFormat {
            message: "Test error".to_string(),
        };
        
        let debug = format!("{:?}", error);
        assert!(debug.contains("InvalidFormat"));
    }
}
