//! Async utilities and helpers

use std::future::Future;
use std::pin::Pin;
use std::task::{Context, Poll};
use std::time::Duration;
use tokio::time::{timeout, Timeout};

/// Async result type
pub type AsyncResult<T> = Result<T, Box<dyn std::error::Error + Send + Sync>>;

/// Timeout wrapper for futures
pub struct TimeoutWrapper<F> {
    inner: Timeout<F>,
}

impl<F> TimeoutWrapper<F>
where
    F: Future,
{
    /// Create new timeout wrapper
    pub fn new(future: F, duration: Duration) -> Self {
        Self {
            inner: timeout(duration, future),
        }
    }
}

impl<F> Future for TimeoutWrapper<F>
where
    F: Future,
{
    type Output = Result<F::Output, tokio::time::error::Elapsed>;

    fn poll(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Self::Output> {
        Pin::new(&mut self.inner).poll(cx)
    }
}

/// Timeout helper functions
pub mod timeout_helpers {
    use super::*;
    
    /// Apply timeout to future
    pub fn with_timeout<F>(future: F, duration: Duration) -> TimeoutWrapper<F>
    where
        F: Future,
    {
        TimeoutWrapper::new(future, duration)
    }
    
    /// Apply default timeout (30 seconds)
    pub fn with_default_timeout<F>(future: F) -> TimeoutWrapper<F>
    where
        F: Future,
    {
        with_timeout(future, Duration::from_secs(30))
    }
    
    /// Apply short timeout (5 seconds)
    pub fn with_short_timeout<F>(future: F) -> TimeoutWrapper<F>
    where
        F: Future,
    {
        with_timeout(future, Duration::from_secs(5))
    }
    
    /// Apply long timeout (5 minutes)
    pub fn with_long_timeout<F>(future: F) -> TimeoutWrapper<F>
    where
        F: Future,
    {
        with_timeout(future, Duration::from_secs(300))
    }
}

/// Retry utilities
pub mod retry {
    use super::*;
    use std::fmt;
    
    /// Retry configuration
    #[derive(Debug, Clone)]
    pub struct RetryConfig {
        pub max_attempts: usize,
        pub base_delay: Duration,
        pub max_delay: Duration,
        pub backoff_multiplier: f64,
    }
    
    impl Default for RetryConfig {
        fn default() -> Self {
            Self {
                max_attempts: 3,
                base_delay: Duration::from_millis(100),
                max_delay: Duration::from_secs(10),
                backoff_multiplier: 2.0,
            }
        }
    }
    
    /// Retry error
    #[derive(Debug)]
    pub struct RetryError {
        pub attempts: usize,
        pub last_error: Box<dyn std::error::Error + Send + Sync>,
    }
    
    impl fmt::Display for RetryError {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            write!(f, "Failed after {} attempts: {}", self.attempts, self.last_error)
        }
    }
    
    impl std::error::Error for RetryError {}
    
    /// Retry a future with exponential backoff
    pub async fn retry_with_backoff<F, Fut, T, E>(
        mut operation: F,
        config: RetryConfig,
    ) -> Result<T, RetryError>
    where
        F: FnMut() -> Fut,
        Fut: Future<Output = Result<T, E>>,
        E: std::error::Error + Send + Sync + 'static,
    {
        let mut delay = config.base_delay;
        
        for attempt in 1..=config.max_attempts {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    if attempt == config.max_attempts {
                        return Err(RetryError {
                            attempts: attempt,
                            last_error: Box::new(error),
                        });
                    }
                    
                    // Wait before retry
                    tokio::time::sleep(delay).await;
                    
                    // Exponential backoff
                    delay = std::cmp::min(
                        Duration::from_millis(
                            (delay.as_millis() as f64 * config.backoff_multiplier) as u64
                        ),
                        config.max_delay,
                    );
                }
            }
        }
        
        unreachable!()
    }
    
    /// Retry with default configuration
    pub async fn retry<F, Fut, T, E>(operation: F) -> Result<T, RetryError>
    where
        F: FnMut() -> Fut,
        Fut: Future<Output = Result<T, E>>,
        E: std::error::Error + Send + Sync + 'static,
    {
        retry_with_backoff(operation, RetryConfig::default()).await
    }
}

/// Rate limiting utilities
pub mod rate_limit {
    use super::*;
    use std::sync::Arc;
    use tokio::sync::Semaphore;
    use tokio::time::Instant;
    
    /// Rate limiter
    #[derive(Debug)]
    pub struct RateLimiter {
        semaphore: Arc<Semaphore>,
        permits: usize,
        window: Duration,
        last_reset: std::sync::Mutex<Instant>,
    }
    
    impl RateLimiter {
        /// Create new rate limiter
        pub fn new(permits: usize, window: Duration) -> Self {
            Self {
                semaphore: Arc::new(Semaphore::new(permits)),
                permits,
                window,
                last_reset: std::sync::Mutex::new(Instant::now()),
            }
        }
        
        /// Acquire permit (blocks if rate limit exceeded)
        pub async fn acquire(&self) -> tokio::sync::SemaphorePermit<'_> {
            // Check if we need to reset the window
            {
                let mut last_reset = self.last_reset.lock().unwrap();
                let now = Instant::now();
                if now.duration_since(*last_reset) >= self.window {
                    // Reset the semaphore
                    let available = self.semaphore.available_permits();
                    if available < self.permits {
                        self.semaphore.add_permits(self.permits - available);
                    }
                    *last_reset = now;
                }
            }
            
            self.semaphore.acquire().await.unwrap()
        }
        
        /// Try to acquire permit (non-blocking)
        pub fn try_acquire(&self) -> Option<tokio::sync::SemaphorePermit<'_>> {
            // Check if we need to reset the window
            {
                let mut last_reset = self.last_reset.lock().unwrap();
                let now = Instant::now();
                if now.duration_since(*last_reset) >= self.window {
                    // Reset the semaphore
                    let available = self.semaphore.available_permits();
                    if available < self.permits {
                        self.semaphore.add_permits(self.permits - available);
                    }
                    *last_reset = now;
                }
            }
            
            self.semaphore.try_acquire().ok()
        }
        
        /// Get available permits
        pub fn available_permits(&self) -> usize {
            self.semaphore.available_permits()
        }
    }
}

/// Async batch processing utilities
pub mod batch {
    use super::*;
    use std::collections::VecDeque;
    
    /// Batch processor
    pub struct BatchProcessor<T> {
        batch_size: usize,
        timeout: Duration,
        buffer: VecDeque<T>,
    }
    
    impl<T> BatchProcessor<T> {
        /// Create new batch processor
        pub fn new(batch_size: usize, timeout: Duration) -> Self {
            Self {
                batch_size,
                timeout,
                buffer: VecDeque::new(),
            }
        }
        
        /// Add item to batch
        pub fn add(&mut self, item: T) {
            self.buffer.push_back(item);
        }
        
        /// Check if batch is ready
        pub fn is_ready(&self) -> bool {
            self.buffer.len() >= self.batch_size
        }
        
        /// Take current batch
        pub fn take_batch(&mut self) -> Vec<T> {
            let count = std::cmp::min(self.batch_size, self.buffer.len());
            self.buffer.drain(..count).collect()
        }
        
        /// Take all remaining items
        pub fn take_all(&mut self) -> Vec<T> {
            self.buffer.drain(..).collect()
        }
        
        /// Get buffer size
        pub fn len(&self) -> usize {
            self.buffer.len()
        }
        
        /// Check if buffer is empty
        pub fn is_empty(&self) -> bool {
            self.buffer.is_empty()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_timeout_wrapper() {
        // Fast operation should succeed
        let result = timeout_helpers::with_short_timeout(async {
            tokio::time::sleep(Duration::from_millis(100)).await;
            42
        }).await;
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 42);
        
        // Slow operation should timeout
        let result = timeout_helpers::with_short_timeout(async {
            tokio::time::sleep(Duration::from_secs(10)).await;
            42
        }).await;
        
        assert!(result.is_err());
    }
    
    #[tokio::test]
    async fn test_retry() {
        use retry::*;
        
        let mut attempts = 0;
        let result = retry(|| {
            attempts += 1;
            async move {
                if attempts < 3 {
                    Err(std::io::Error::new(std::io::ErrorKind::Other, "fail"))
                } else {
                    Ok(42)
                }
            }
        }).await;
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 42);
        assert_eq!(attempts, 3);
    }
    
    #[tokio::test]
    async fn test_rate_limiter() {
        use rate_limit::*;
        
        let limiter = RateLimiter::new(2, Duration::from_secs(1));
        
        // Should be able to acquire 2 permits
        let _permit1 = limiter.acquire().await;
        let _permit2 = limiter.acquire().await;
        
        // Third permit should not be available immediately
        assert!(limiter.try_acquire().is_none());
    }
    
    #[test]
    fn test_batch_processor() {
        use batch::*;
        
        let mut processor = BatchProcessor::new(3, Duration::from_secs(1));
        
        assert!(!processor.is_ready());
        assert_eq!(processor.len(), 0);
        
        processor.add(1);
        processor.add(2);
        assert!(!processor.is_ready());
        
        processor.add(3);
        assert!(processor.is_ready());
        
        let batch = processor.take_batch();
        assert_eq!(batch, vec![1, 2, 3]);
        assert_eq!(processor.len(), 0);
    }
}
