{"rustc": 12200708597866198150, "features": "[\"default\", \"logging\", \"prettyplease\", \"runtime\", \"which-rustfmt\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_16\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\", \"which-rustfmt\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 5411960195569474477, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bindgen-1c27d5e129767fa5\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}