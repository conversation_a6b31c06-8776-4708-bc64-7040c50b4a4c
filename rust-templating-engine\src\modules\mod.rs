//! Module system

use crate::error::Result;
use crate::template::Placeholder;

/// Module trait for extending template functionality
pub trait Module: Send + Sync {
    /// Module name
    fn name(&self) -> &str;

    /// Process placeholder
    fn process(&self, placeholder: &Placeholder, data: &serde_json::Value) -> Result<String>;

    /// Check if module can handle placeholder
    fn can_handle(&self, placeholder: &Placeholder) -> bool;
}

/// Module manager
pub struct ModuleManager {
    modules: Vec<Box<dyn Module>>,
}

/// Common module for basic operations
#[derive(Debug)]
pub struct CommonModule;

/// Loop module for handling loops
#[derive(Debug)]
pub struct LoopModule;

/// Condition module for handling conditions
#[derive(Debug)]
pub struct ConditionModule;

impl Module for CommonModule {
    fn name(&self) -> &str {
        "common"
    }

    fn process(&self, placeholder: &Placeholder, data: &serde_json::Value) -> Result<String> {
        // Simple value replacement
        if let Some(value) = data.get(&placeholder.name) {
            Ok(match value {
                serde_json::Value::String(s) => s.clone(),
                serde_json::Value::Number(n) => n.to_string(),
                serde_json::Value::Bool(b) => b.to_string(),
                _ => value.to_string(),
            })
        } else {
            Ok(String::new())
        }
    }

    fn can_handle(&self, placeholder: &Placeholder) -> bool {
        matches!(placeholder.placeholder_type, crate::template::PlaceholderType::Simple)
    }
}

impl Module for LoopModule {
    fn name(&self) -> &str {
        "loop"
    }

    fn process(&self, _placeholder: &Placeholder, _data: &serde_json::Value) -> Result<String> {
        // Placeholder loop implementation
        Ok(String::new())
    }

    fn can_handle(&self, placeholder: &Placeholder) -> bool {
        matches!(placeholder.placeholder_type, crate::template::PlaceholderType::Loop { .. })
    }
}

impl Module for ConditionModule {
    fn name(&self) -> &str {
        "condition"
    }

    fn process(&self, _placeholder: &Placeholder, _data: &serde_json::Value) -> Result<String> {
        // Placeholder condition implementation
        Ok(String::new())
    }

    fn can_handle(&self, placeholder: &Placeholder) -> bool {
        matches!(placeholder.placeholder_type, crate::template::PlaceholderType::Condition { .. })
    }
}

impl ModuleManager {
    pub fn new() -> Self {
        Self {
            modules: Vec::new(),
        }
    }

    pub fn with_default_modules() -> Self {
        let mut manager = Self::new();
        manager.register(Box::new(CommonModule));
        manager.register(Box::new(LoopModule));
        manager.register(Box::new(ConditionModule));
        manager
    }

    pub fn register(&mut self, module: Box<dyn Module>) {
        self.modules.push(module);
    }

    pub fn process(&self, placeholder: &Placeholder, data: &serde_json::Value) -> Result<String> {
        for module in &self.modules {
            if module.can_handle(placeholder) {
                return module.process(placeholder, data);
            }
        }

        // No module can handle this placeholder
        Ok(format!("{{{}}}", placeholder.name))
    }

    pub fn get_module(&self, name: &str) -> Option<&dyn Module> {
        self.modules.iter()
            .find(|m| m.name() == name)
            .map(|m| m.as_ref())
    }

    pub fn list_modules(&self) -> Vec<&str> {
        self.modules.iter().map(|m| m.name()).collect()
    }
}
