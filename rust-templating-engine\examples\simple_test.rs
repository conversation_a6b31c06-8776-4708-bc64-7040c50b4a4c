use rust_templating_engine::{Template<PERSON><PERSON><PERSON>, EngineConfig, Result};
use serde_json::json;

/// Simple test để verify engine hoạt động
#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 SIMPLE ENGINE TEST");
    println!("=" .repeat(30));
    
    // Tạo engine
    let config = EngineConfig::builder()
        .memory_limit(64 * 1024 * 1024) // 64MB
        .delimiters("{", "}")
        .build()?;
    
    let engine = TemplateEngine::new(config);
    println!("✅ Engine created successfully");
    
    // Test data giống shipping record
    let test_data = json!({
        "bill": "TW00123456",
        "receiver": "Test Company Ltd.",
        "shpper": "Sender Corp",
        "created": "2024-01-15T10:30:00.000",
        "weight": 2.5
    });
    
    // Simple template
    let template = "Bill: {bill}\nReceiver: {receiver}\nWeight: {weight}kg";
    
    println!("\n📝 Processing template...");
    let result = engine.process_content(template, &test_data).await?;
    
    println!("✅ Template processed successfully!");
    println!("\n📄 Result:");
    println!("{}", result);
    
    println!("\n🎉 Simple test completed!");
    
    Ok(())
}
