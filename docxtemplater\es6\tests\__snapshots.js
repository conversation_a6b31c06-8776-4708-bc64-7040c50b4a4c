/***************** This file is autogenerated *****************************

   Please don't modify it manually !
   Instead, to update the file, run :

   npm run test:es6:update-snapshots
*/

exports[`Api versioning should fail with invalid versions`] = {
  "_type": "XTAPIVersionError",
  "name": "APIVersionError",
  "message": "neededVersion is not a valid version",
  "properties": {
    "id": "api_version_error",
    "neededVersion": [
      5,
      0
    ],
    "explanation": "the neededVersion must be an array of length 3"
  }
}

exports[`Api versioning should fail with invalid versions-1`] = {
  "_type": "XTAPIVersionError",
  "name": "APIVersionError",
  "message": "The major api version do not match, you probably have to update docxtemplater with npm install --save docxtemplater",
  "properties": {
    "id": "api_version_error",
    "neededVersion": [
      5,
      6,
      0
    ],
    "currentModuleApiVersion": [
      3,
      47,
      0
    ],
    "explanation": "moduleAPIVersionMismatch : needed=5.6.0, current=3.47.0"
  }
}

exports[`Api versioning should fail with invalid versions-2`] = {
  "_type": "XTAPIVersionError",
  "name": "APIVersionError",
  "message": "The minor api version is not uptodate, you probably have to update docxtemplater with npm install --save docxtemplater",
  "properties": {
    "id": "api_version_error",
    "neededVersion": [
      3,
      48,
      0
    ],
    "currentModuleApiVersion": [
      3,
      47,
      0
    ],
    "explanation": "moduleAPIVersionMismatch : needed=3.48.0, current=3.47.0"
  }
}

exports[`Api versioning should fail with invalid versions-3`] = {
  "_type": "XTAPIVersionError",
  "name": "APIVersionError",
  "message": "The patch api version is not uptodate, you probably have to update docxtemplater with npm install --save docxtemplater",
  "properties": {
    "id": "api_version_error",
    "neededVersion": [
      3,
      47,
      100
    ],
    "currentModuleApiVersion": [
      3,
      47,
      0
    ],
    "explanation": "moduleAPIVersionMismatch : needed=3.47.100, current=3.47.0"
  }
}

exports[`Compilation errors should count 3 errors when having rawxml and two other errors`] = {
  "_type": "XTTemplateError",
  "name": "TemplateError",
  "message": "Multi error",
  "properties": {
    "errors": [
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unopened tag",
        "properties": {
          "xtag": "foo",
          "id": "unopened_tag",
          "context": "foo",
          "offset": 3,
          "explanation": "The tag beginning with \"foo\" is unopened",
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unopened tag",
        "properties": {
          "xtag": "bar",
          "id": "unopened_tag",
          "context": "} bar",
          "offset": 16,
          "explanation": "The tag beginning with \"} bar\" is unopened",
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Raw tag should be the only text in paragraph",
        "properties": {
          "id": "raw_xml_tag_should_be_only_text_in_paragraph",
          "explanation": "The raw tag \"bang\" should be the only text in this paragraph. This means that this tag should not be surrounded by any text or spaces.",
          "xtag": "bang",
          "offset": 5,
          "paragraphParts": [
            {
              "type": "tag",
              "position": "start",
              "text": false,
              "value": "<w:r>",
              "tag": "w:r",
              "lIndex": 1
            },
            {
              "type": "tag",
              "position": "start",
              "text": true,
              "value": "<w:t xml:space=\"preserve\">",
              "tag": "w:t",
              "lIndex": 2
            },
            {
              "type": "content",
              "value": "foo",
              "position": "insidetag",
              "lIndex": 3
            },
            {
              "type": "placeholder",
              "value": "",
              "endLindex": 4,
              "lIndex": 4
            },
            {
              "type": "content",
              "value": " ",
              "position": "insidetag",
              "lIndex": 5
            },
            {
              "type": "placeholder",
              "module": "rawxml",
              "value": "bang",
              "offset": 5,
              "endLindex": 8,
              "lIndex": 8,
              "raw": "@bang"
            },
            {
              "type": "content",
              "value": " bar",
              "position": "insidetag",
              "lIndex": 9
            },
            {
              "type": "placeholder",
              "value": "",
              "offset": 5,
              "endLindex": 10,
              "lIndex": 10
            },
            {
              "type": "tag",
              "position": "end",
              "text": true,
              "value": "</w:t>",
              "tag": "w:t",
              "lIndex": 11
            },
            {
              "type": "tag",
              "position": "end",
              "text": false,
              "value": "</w:r>",
              "tag": "w:r",
              "lIndex": 12
            }
          ],
          "file": "word/document.xml"
        }
      }
    ],
    "id": "multi_error",
    "explanation": "The template has multiple errors"
  }
}

exports[`Compilation errors should fail early when a loop closes the wrong loop`] = {
  "_type": "XTTemplateError",
  "name": "TemplateError",
  "message": "Multi error",
  "properties": {
    "errors": [
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unopened loop",
        "properties": {
          "id": "unopened_loop",
          "explanation": "The loop with tag \"loop3\" is unopened",
          "xtag": "loop3",
          "offset": 16,
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unopened loop",
        "properties": {
          "id": "unopened_loop",
          "explanation": "The loop with tag \"loop3\" is unopened",
          "xtag": "loop3",
          "offset": 24,
          "file": "word/document.xml"
        }
      }
    ],
    "id": "multi_error",
    "explanation": "The template has multiple errors"
  }
}

exports[`Compilation errors should fail when rawtag is in table without paragraph`] = {
  "_type": "XTTemplateError",
  "name": "TemplateError",
  "message": "Multi error",
  "properties": {
    "errors": [
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Raw tag not in paragraph",
        "properties": {
          "id": "raw_tag_outerxml_invalid",
          "explanation": "The tag \"myrawtag\" is not inside a paragraph, putting raw tags inside an inline loop is disallowed.",
          "rootError": {
            "_type": "XTTemplateError",
            "name": "TemplateError",
            "message": "No tag \"w:p\" was found at the left",
            "properties": {
              "id": "no_xml_tag_found_at_left",
              "explanation": "No tag \"w:p\" was found at the left",
              "offset": 0,
              "part": {
                "type": "placeholder",
                "module": "rawxml",
                "value": "myrawtag",
                "offset": 0,
                "endLindex": 4,
                "lIndex": 4,
                "raw": "@myrawtag"
              },
              "parsed": [
                {
                  "type": "tag",
                  "position": "start",
                  "text": false,
                  "value": "<w:tbl>",
                  "tag": "w:tbl",
                  "lIndex": 0
                },
                {
                  "type": "tag",
                  "position": "start",
                  "text": true,
                  "value": "<w:t xml:space=\"preserve\">",
                  "tag": "w:t",
                  "lIndex": 1
                },
                {
                  "type": "placeholder",
                  "module": "rawxml",
                  "value": "myrawtag",
                  "offset": 0,
                  "endLindex": 4,
                  "lIndex": 4,
                  "raw": "@myrawtag"
                },
                {
                  "type": "tag",
                  "position": "end",
                  "text": true,
                  "value": "</w:t>",
                  "tag": "w:t",
                  "lIndex": 5
                },
                {
                  "type": "tag",
                  "position": "end",
                  "text": false,
                  "value": "</w:p>",
                  "tag": "w:p",
                  "lIndex": 6
                },
                {
                  "type": "tag",
                  "position": "end",
                  "text": false,
                  "value": "</w:tbl>",
                  "tag": "w:tbl",
                  "lIndex": 7
                }
              ],
              "index": 2,
              "element": "w:p"
            }
          },
          "xtag": "myrawtag",
          "offset": 0,
          "postparsed": [
            {
              "type": "tag",
              "position": "start",
              "text": false,
              "value": "<w:tbl>",
              "tag": "w:tbl",
              "lIndex": 0
            },
            {
              "type": "tag",
              "position": "start",
              "text": true,
              "value": "<w:t xml:space=\"preserve\">",
              "tag": "w:t",
              "lIndex": 1
            },
            {
              "type": "placeholder",
              "module": "rawxml",
              "value": "myrawtag",
              "offset": 0,
              "endLindex": 4,
              "lIndex": 4,
              "raw": "@myrawtag"
            },
            {
              "type": "tag",
              "position": "end",
              "text": true,
              "value": "</w:t>",
              "tag": "w:t",
              "lIndex": 5
            },
            {
              "type": "tag",
              "position": "end",
              "text": false,
              "value": "</w:p>",
              "tag": "w:p",
              "lIndex": 6
            },
            {
              "type": "tag",
              "position": "end",
              "text": false,
              "value": "</w:tbl>",
              "tag": "w:tbl",
              "lIndex": 7
            }
          ],
          "expandTo": "w:p",
          "index": 2,
          "file": "word/document.xml"
        }
      }
    ],
    "id": "multi_error",
    "explanation": "The template has multiple errors"
  }
}

exports[`Compilation errors should fail when rawtag is not in paragraph`] = {
  "_type": "XTTemplateError",
  "name": "TemplateError",
  "message": "Multi error",
  "properties": {
    "errors": [
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Raw tag not in paragraph",
        "properties": {
          "id": "raw_tag_outerxml_invalid",
          "explanation": "The tag \"myrawtag\" is not inside a paragraph, putting raw tags inside an inline loop is disallowed.",
          "rootError": {
            "_type": "XTTemplateError",
            "name": "TemplateError",
            "message": "No tag \"w:p\" was found at the left",
            "properties": {
              "id": "no_xml_tag_found_at_left",
              "explanation": "No tag \"w:p\" was found at the left",
              "offset": 0,
              "part": {
                "type": "placeholder",
                "module": "rawxml",
                "value": "myrawtag",
                "offset": 0,
                "endLindex": 3,
                "lIndex": 3,
                "raw": "@myrawtag"
              },
              "parsed": [
                {
                  "type": "tag",
                  "position": "start",
                  "text": true,
                  "value": "<w:t xml:space=\"preserve\">",
                  "tag": "w:t",
                  "lIndex": 0
                },
                {
                  "type": "placeholder",
                  "module": "rawxml",
                  "value": "myrawtag",
                  "offset": 0,
                  "endLindex": 3,
                  "lIndex": 3,
                  "raw": "@myrawtag"
                },
                {
                  "type": "tag",
                  "position": "end",
                  "text": true,
                  "value": "</w:t>",
                  "tag": "w:t",
                  "lIndex": 4
                }
              ],
              "index": 1,
              "element": "w:p"
            }
          },
          "xtag": "myrawtag",
          "offset": 0,
          "postparsed": [
            {
              "type": "tag",
              "position": "start",
              "text": true,
              "value": "<w:t xml:space=\"preserve\">",
              "tag": "w:t",
              "lIndex": 0
            },
            {
              "type": "placeholder",
              "module": "rawxml",
              "value": "myrawtag",
              "offset": 0,
              "endLindex": 3,
              "lIndex": 3,
              "raw": "@myrawtag"
            },
            {
              "type": "tag",
              "position": "end",
              "text": true,
              "value": "</w:t>",
              "tag": "w:t",
              "lIndex": 4
            }
          ],
          "expandTo": "w:p",
          "index": 1,
          "file": "word/document.xml"
        }
      }
    ],
    "id": "multi_error",
    "explanation": "The template has multiple errors"
  }
}

exports[`Inspect module should get all tags (pptx file)`] = [
  {
    "type": "placeholder",
    "value": "tag",
    "offset": 0,
    "endLindex": 36,
    "lIndex": 36
  }
]

exports[`Inspect module should get main tags`] = [
  {
    "type": "placeholder",
    "module": "loop",
    "inverted": false,
    "value": "offre",
    "offset": 29,
    "endLindex": 332,
    "lIndex": 188,
    "raw": "#offre",
    "sectPrCount": 0,
    "lastParagrapSectPr": "",
    "subparsed": [
      {
        "type": "placeholder",
        "value": "nom",
        "offset": 37,
        "endLindex": 214,
        "lIndex": 214
      },
      {
        "type": "placeholder",
        "value": "prix",
        "offset": 48,
        "endLindex": 241,
        "lIndex": 241
      },
      {
        "type": "placeholder",
        "value": "titre",
        "offset": 60,
        "endLindex": 268,
        "lIndex": 268
      }
    ]
  },
  {
    "type": "placeholder",
    "value": "nom",
    "offset": 75,
    "endLindex": 354,
    "lIndex": 354
  },
  {
    "type": "placeholder",
    "value": "prenom",
    "offset": 80,
    "endLindex": 378,
    "lIndex": 378
  }
]

exports[`Multi errors should work with loops unopened`] = {
  "_type": "XTTemplateError",
  "name": "TemplateError",
  "message": "Multi error",
  "properties": {
    "errors": [
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unopened loop",
        "properties": {
          "id": "unopened_loop",
          "explanation": "The loop with tag \"loop\" is unopened",
          "xtag": "loop",
          "offset": 0,
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Closing tag does not match opening tag",
        "properties": {
          "id": "closing_tag_does_not_match_opening_tag",
          "explanation": "The tag \"users\" is closed by the tag \"foo\"",
          "openingtag": "users",
          "offset": [
            8,
            25
          ],
          "closingtag": "foo",
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Closing tag does not match opening tag",
        "properties": {
          "id": "closing_tag_does_not_match_opening_tag",
          "explanation": "The tag \"bang\" is closed by the tag \"baz\"",
          "openingtag": "bang",
          "offset": [
            36,
            52
          ],
          "closingtag": "baz",
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unopened loop",
        "properties": {
          "id": "unopened_loop",
          "explanation": "The loop with tag \"fff\" is unopened",
          "xtag": "fff",
          "offset": 63,
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unclosed loop",
        "properties": {
          "id": "unclosed_loop",
          "explanation": "The loop with tag \"yum\" is unclosed",
          "xtag": "yum",
          "offset": 74,
          "file": "word/document.xml"
        }
      }
    ],
    "id": "multi_error",
    "explanation": "The template has multiple errors"
  }
}

exports[`Multi errors should work with multiple errors complex`] = {
  "_type": "XTTemplateError",
  "name": "TemplateError",
  "message": "Multi error",
  "properties": {
    "errors": [
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unopened tag",
        "properties": {
          "xtag": "foo",
          "id": "unopened_tag",
          "context": "foo",
          "offset": 3,
          "explanation": "The tag beginning with \"foo\" is unopened",
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unclosed tag",
        "properties": {
          "xtag": "user,",
          "id": "unclosed_tag",
          "context": "{user, my age is ",
          "offset": 11,
          "explanation": "The tag beginning with \"{user, my \" is unclosed",
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unopened tag",
        "properties": {
          "xtag": "bang",
          "id": "unopened_tag",
          "context": "}!Hi bang",
          "offset": 41,
          "explanation": "The tag beginning with \"}!Hi bang\" is unopened",
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unclosed tag",
        "properties": {
          "xtag": "user,",
          "id": "unclosed_tag",
          "context": "{user, my age is ",
          "offset": 123,
          "explanation": "The tag beginning with \"{user, my \" is unclosed",
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Unclosed tag",
        "properties": {
          "xtag": "bar!",
          "id": "unclosed_tag",
          "context": "{bar!",
          "offset": 140,
          "explanation": "The tag beginning with \"{bar!\" is unclosed",
          "file": "word/document.xml"
        }
      }
    ],
    "id": "multi_error",
    "explanation": "The template has multiple errors"
  }
}

exports[`ParagraphLoop should not fail when having paragraph in paragraph`] = `
(0)<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
(0)<w:document xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas" xmlns:cx="http://schemas.microsoft.com/office/drawing/2014/chartex" xmlns:cx1="http://schemas.microsoft.com/office/drawing/2015/9/8/chartex" xmlns:cx2="http://schemas.microsoft.com/office/drawing/2015/10/21/chartex" xmlns:cx3="http://schemas.microsoft.com/office/drawing/2016/5/9/chartex" xmlns:cx4="http://schemas.microsoft.com/office/drawing/2016/5/10/chartex" xmlns:cx5="http://schemas.microsoft.com/office/drawing/2016/5/11/chartex" xmlns:cx6="http://schemas.microsoft.com/office/drawing/2016/5/12/chartex" xmlns:cx7="http://schemas.microsoft.com/office/drawing/2016/5/13/chartex" xmlns:cx8="http://schemas.microsoft.com/office/drawing/2016/5/14/chartex" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:aink="http://schemas.microsoft.com/office/drawing/2016/ink" xmlns:am3d="http://schemas.microsoft.com/office/drawing/2017/model3d" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml" xmlns:w16cid="http://schemas.microsoft.com/office/word/2016/wordml/cid" xmlns:w16se="http://schemas.microsoft.com/office/word/2015/wordml/symex" xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup" xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape" mc:Ignorable="w14 w15 w16se w16cid wp14">
(1)   <w:body>
*********START LOOP OF hi
(3)         <w:p>
(4)            <w:r>
(5)               <w:t xml:space="preserve">
(5)               </w:t>
(4)            </w:r>
(4)            <w:p w14:paraId="736A2D9F" w14:textId="77777777" w:rsidR="008E1095" w:rsidRDefault="008E1095"/>
(4)            <w:sdt>
(5)               <w:sdtPr><w:alias w:val="SF:Table"/><w:id w:val="2039776175"/><w:placeholder><w:docPart w:val="CFCF8C7E6B33489A9DD50D7417AD410D"/></w:placeholder><w15:color w:val="008000"/><w15:appearance w15:val="hidden"/></w:sdtPr>
(5)               <w:sdtContent>
(6)                  <w:p w14:paraId="4800949B" w14:textId="77777777" w:rsidR="00D3440B" w:rsidRDefault="00D3440B" w:rsidP="00D3440B"/>
(6)                  <w:tbl>
(7)                     <w:tblPr><w:tblStyle w:val="TableGrid"/><w:tblW w:w="0" w:type="auto"/><w:tblLook w:val="0660" w:firstRow="1" w:lastRow="1" w:firstColumn="0" w:lastColumn="0" w:noHBand="1" w:noVBand="1"/></w:tblPr><w:tblGrid><w:gridCol w:w="2909"/><w:gridCol w:w="2928"/><w:gridCol w:w="2793"/></w:tblGrid>
(7)                     <w:tr w:rsidR="00D3440B" w14:paraId="5887C255" w14:textId="77777777" w:rsidTr="00F617CB">
(8)                        <w:trPr><w:tblHeader/></w:trPr>
(8)                        <w:tc>
(9)                           <w:tcPr><w:tcW w:w="0" w:type="auto"/></w:tcPr>
(9)                           <w:p w14:paraId="5951DD51" w14:textId="77777777" w:rsidR="00D3440B" w:rsidRDefault="00D3440B" w:rsidP="00F617CB">
(10)                              <w:r>
(11)                                 <w:t xml:space="preserve">
(12)                                    name
(11)                                 </w:t>
(10)                              </w:r>
(9)                           </w:p>
(8)                        </w:tc>
(8)                        <w:tc>
(9)                           <w:tcPr><w:tcW w:w="0" w:type="auto"/></w:tcPr>
(9)                           <w:p w14:paraId="34A4528E" w14:textId="77777777" w:rsidR="00D3440B" w:rsidRDefault="00D3440B" w:rsidP="00F617CB">
(10)                              <w:r>
(11)                                 <w:t xml:space="preserve">
(12)                                    phone
(11)                                 </w:t>
(10)                              </w:r>
(9)                           </w:p>
(8)                        </w:tc>
(8)                        <w:tc>
(9)                           <w:tcPr><w:tcW w:w="0" w:type="auto"/></w:tcPr>
(9)                           <w:p w14:paraId="1EFFDB2B" w14:textId="77777777" w:rsidR="00D3440B" w:rsidRDefault="00D3440B" w:rsidP="00F617CB">
(10)                              <w:r>
(11)                                 <w:t xml:space="preserve">
(12)                                    website
(11)                                 </w:t>
(10)                              </w:r>
(9)                           </w:p>
(8)                        </w:tc>
(7)                     </w:tr>
(7)                     <w:tr w:rsidR="00D3440B" w14:paraId="53971E16" w14:textId="77777777" w:rsidTr="00F617CB">
(8)                        <w:sdt>
(9)                           <w:sdtPr><w:alias w:val="SF:R"/><w:id w:val="1927457022"/><w15:color w:val="008000"/><w15:appearance w15:val="hidden"/></w:sdtPr>
(9)                           <w:sdtContent>
(10)                              <w:tc>
(11)                                 <w:tcPr><w:tcW w:w="0" w:type="auto"/></w:tcPr>
(11)                                 <w:p w14:paraId="6B371C2E" w14:textId="77777777" w:rsidR="00D3440B" w:rsidRDefault="00D3440B" w:rsidP="00F617CB">
(12)                                    <w:pPr>
(13)                                       <w:spacing w:line="360" w:lineRule="auto"/>
(13)                                       <w:jc w:val="both"/>
(12)                                    </w:pPr>
(12)                                    <w:r>
(13)                                       <w:t xml:space="preserve">
=============================================={foo}
(13)                                       </w:t>
(12)                                    </w:r>
(11)                                 </w:p>
(10)                              </w:tc>
(9)                           </w:sdtContent>
(8)                        </w:sdt>
(8)                        <w:sdt>
(9)                           <w:sdtPr><w:alias w:val="SF:R"/><w:id w:val="1556285925"/><w15:color w:val="008000"/><w15:appearance w15:val="hidden"/></w:sdtPr>
(9)                           <w:sdtContent>
(10)                              <w:tc>
(11)                                 <w:tcPr><w:tcW w:w="0" w:type="auto"/></w:tcPr>
(11)                                 <w:p w14:paraId="3C4100BB" w14:textId="77777777" w:rsidR="00D3440B" w:rsidRDefault="00D3440B" w:rsidP="00F617CB">
(12)                                    <w:pPr>
(13)                                       <w:spacing w:line="360" w:lineRule="auto"/>
(13)                                       <w:jc w:val="both"/>
(12)                                    </w:pPr>
(12)                                    <w:r>
(13)                                       <w:t xml:space="preserve">
=============================================={bar}
(13)                                       </w:t>
(12)                                    </w:r>
(11)                                 </w:p>
(10)                              </w:tc>
(9)                           </w:sdtContent>
(8)                        </w:sdt>
(8)                        <w:sdt>
(9)                           <w:sdtPr><w:alias w:val="SF:R"/><w:id w:val="-788971804"/><w15:color w:val="008000"/><w15:appearance w15:val="hidden"/></w:sdtPr>
(9)                           <w:sdtContent>
(10)                              <w:tc>
(11)                                 <w:tcPr><w:tcW w:w="0" w:type="auto"/></w:tcPr>
(11)                                 <w:p w14:paraId="24C5CEB1" w14:textId="77777777" w:rsidR="00D3440B" w:rsidRDefault="00D3440B" w:rsidP="00F617CB">
(12)                                    <w:pPr>
(13)                                       <w:spacing w:line="360" w:lineRule="auto"/>
(13)                                       <w:jc w:val="both"/>
(12)                                    </w:pPr>
(12)                                    <w:r>
(13)                                       <w:t xml:space="preserve">
=============================================={bar}
(13)                                       </w:t>
(12)                                    </w:r>
(11)                                 </w:p>
(10)                              </w:tc>
(9)                           </w:sdtContent>
(8)                        </w:sdt>
(7)                     </w:tr>
(6)                  </w:tbl>
(6)                  <w:p w14:paraId="676A1E43" w14:textId="77777777" w:rsidR="00D3440B" w:rsidRDefault="00D3440B" w:rsidP="00D3440B"/>
(5)               </w:sdtContent>
(4)            </w:sdt>
(4)            <w:p w14:paraId="22D858B2" w14:textId="69A6ACE6" w:rsidR="009843AE" w:rsidRDefault="00EB53B3"/>
(3)         </w:p>
*********END LOOP OF hi
(1)   </w:body>
(0)</w:document>`

exports[`Pptx generation should work with loop table`] = [
  {
    "tag": "products",
    "lIndex": 59,
    "value": [
      [
        {
          "tag": "name",
          "lIndex": 62,
          "value": "Acme"
        },
        {
          "tag": "price",
          "lIndex": 80,
          "value": 10
        }
      ],
      [
        {
          "tag": "name",
          "lIndex": 62,
          "value": "Ecma"
        },
        {
          "tag": "price",
          "lIndex": 80,
          "value": 20
        }
      ]
    ]
  }
]

exports[`Traits should just call onError but keep it if the return value is not a string`] = {
  "_type": "XTTemplateError",
  "name": "TemplateError",
  "message": "Multi error",
  "properties": {
    "errors": [
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Raw tag not in paragraph",
        "properties": {
          "id": "raw_tag_outerxml_invalid",
          "explanation": "The tag \"user\" is not inside a paragraph",
          "rootError": {
            "_type": "XTTemplateError",
            "name": "TemplateError",
            "message": "No tag \"w:p\" was found at the left",
            "properties": {
              "id": "no_xml_tag_found_at_left",
              "explanation": "No tag \"w:p\" was found at the left",
              "offset": 4,
              "part": {
                "type": "placeholder",
                "module": "foo_module/foo",
                "value": "user",
                "offset": 4,
                "endLindex": 4,
                "lIndex": 4,
                "raw": "__user"
              },
              "parsed": [
                {
                  "type": "tag",
                  "position": "start",
                  "text": true,
                  "value": "<w:t xml:space=\"preserve\">",
                  "tag": "w:t",
                  "lIndex": 0
                },
                {
                  "type": "content",
                  "value": "Foo ",
                  "position": "insidetag",
                  "lIndex": 1
                },
                {
                  "type": "placeholder",
                  "module": "foo_module/foo",
                  "value": "user",
                  "offset": 4,
                  "endLindex": 4,
                  "lIndex": 4,
                  "raw": "__user"
                },
                {
                  "type": "content",
                  "value": " ",
                  "position": "insidetag",
                  "lIndex": 5
                },
                {
                  "type": "placeholder",
                  "module": "foo_module/foo",
                  "value": "bar",
                  "offset": 13,
                  "endLindex": 8,
                  "lIndex": 8,
                  "raw": "__bar"
                },
                {
                  "type": "tag",
                  "position": "end",
                  "text": true,
                  "value": "</w:t>",
                  "tag": "w:t",
                  "lIndex": 9
                },
                {
                  "type": "tag",
                  "position": "end",
                  "text": false,
                  "value": "</w:p>",
                  "tag": "w:p",
                  "lIndex": 10
                }
              ],
              "index": 2,
              "element": "w:p"
            }
          },
          "xtag": "user",
          "offset": 4,
          "postparsed": [
            {
              "type": "tag",
              "position": "start",
              "text": true,
              "value": "<w:t xml:space=\"preserve\">",
              "tag": "w:t",
              "lIndex": 0
            },
            {
              "type": "content",
              "value": "Foo ",
              "position": "insidetag",
              "lIndex": 1
            },
            {
              "type": "placeholder",
              "module": "foo_module/foo",
              "value": "user",
              "offset": 4,
              "endLindex": 4,
              "lIndex": 4,
              "raw": "__user"
            },
            {
              "type": "content",
              "value": " ",
              "position": "insidetag",
              "lIndex": 5
            },
            {
              "type": "placeholder",
              "module": "foo_module/foo",
              "value": "bar",
              "offset": 13,
              "endLindex": 8,
              "lIndex": 8,
              "raw": "__bar"
            },
            {
              "type": "tag",
              "position": "end",
              "text": true,
              "value": "</w:t>",
              "tag": "w:t",
              "lIndex": 9
            },
            {
              "type": "tag",
              "position": "end",
              "text": false,
              "value": "</w:p>",
              "tag": "w:p",
              "lIndex": 10
            }
          ],
          "expandTo": "w:p",
          "index": 2,
          "file": "word/document.xml"
        }
      },
      {
        "_type": "XTTemplateError",
        "name": "TemplateError",
        "message": "Raw tag not in paragraph",
        "properties": {
          "id": "raw_tag_outerxml_invalid",
          "explanation": "The tag \"bar\" is not inside a paragraph",
          "rootError": {
            "_type": "XTTemplateError",
            "name": "TemplateError",
            "message": "No tag \"w:p\" was found at the left",
            "properties": {
              "id": "no_xml_tag_found_at_left",
              "explanation": "No tag \"w:p\" was found at the left",
              "offset": 13,
              "part": {
                "type": "placeholder",
                "module": "foo_module/foo",
                "value": "bar",
                "offset": 13,
                "endLindex": 8,
                "lIndex": 8,
                "raw": "__bar"
              },
              "parsed": [
                {
                  "type": "tag",
                  "position": "start",
                  "text": true,
                  "value": "<w:t xml:space=\"preserve\">",
                  "tag": "w:t",
                  "lIndex": 0
                },
                {
                  "type": "content",
                  "value": "Foo ",
                  "position": "insidetag",
                  "lIndex": 1
                },
                {
                  "type": "placeholder",
                  "module": "foo_module/foo",
                  "value": "user",
                  "offset": 4,
                  "endLindex": 4,
                  "lIndex": 4,
                  "raw": "__user"
                },
                {
                  "type": "content",
                  "value": " ",
                  "position": "insidetag",
                  "lIndex": 5
                },
                {
                  "type": "placeholder",
                  "module": "foo_module/foo",
                  "value": "bar",
                  "offset": 13,
                  "endLindex": 8,
                  "lIndex": 8,
                  "raw": "__bar"
                },
                {
                  "type": "tag",
                  "position": "end",
                  "text": true,
                  "value": "</w:t>",
                  "tag": "w:t",
                  "lIndex": 9
                },
                {
                  "type": "tag",
                  "position": "end",
                  "text": false,
                  "value": "</w:p>",
                  "tag": "w:p",
                  "lIndex": 10
                }
              ],
              "index": 4,
              "element": "w:p"
            }
          },
          "xtag": "bar",
          "offset": 13,
          "postparsed": [
            {
              "type": "tag",
              "position": "start",
              "text": true,
              "value": "<w:t xml:space=\"preserve\">",
              "tag": "w:t",
              "lIndex": 0
            },
            {
              "type": "content",
              "value": "Foo ",
              "position": "insidetag",
              "lIndex": 1
            },
            {
              "type": "placeholder",
              "module": "foo_module/foo",
              "value": "user",
              "offset": 4,
              "endLindex": 4,
              "lIndex": 4,
              "raw": "__user"
            },
            {
              "type": "content",
              "value": " ",
              "position": "insidetag",
              "lIndex": 5
            },
            {
              "type": "placeholder",
              "module": "foo_module/foo",
              "value": "bar",
              "offset": 13,
              "endLindex": 8,
              "lIndex": 8,
              "raw": "__bar"
            },
            {
              "type": "tag",
              "position": "end",
              "text": true,
              "value": "</w:t>",
              "tag": "w:t",
              "lIndex": 9
            },
            {
              "type": "tag",
              "position": "end",
              "text": false,
              "value": "</w:p>",
              "tag": "w:p",
              "lIndex": 10
            }
          ],
          "expandTo": "w:p",
          "index": 4,
          "file": "word/document.xml"
        }
      }
    ],
    "id": "multi_error",
    "explanation": "The template has multiple errors"
  }
}

exports[`Verify apiversion should fail with invalid api version`] = {
  "_type": "XTAPIVersionError",
  "name": "APIVersionError",
  "message": "The minor api version is not uptodate, you probably have to update docxtemplater with npm install --save docxtemplater",
  "properties": {
    "id": "api_version_error",
    "neededVersion": [
      3,
      92,
      0
    ],
    "currentModuleApiVersion": [
      3,
      47,
      0
    ],
    "explanation": "moduleAPIVersionMismatch : needed=3.92.0, current=3.47.0"
  }
}

exports[`Zip output should work with toBuffer and also verify that the fileorder contains [Content_Types].xml and _rels/.rels in first characters`] = `PK@@--------------------------[Content_Types].xml--OO-@-----f--]----pP-------nlw7--o--1---zi-----m---------I------J-E--g-------Da--l---G7---A-HlB-rD--y-9-----Jf---n--------m---5--c---h---X--M6----P----UV-s-----WF-H-----w---.-----U---N-k--Tx---u----------6-8m-i-G------yY--J---5q-----OQ-----O------C-i-_--4@--.---7--a----7-F--Z4--x-G-F------s-B-B-----6n-----[-S--------------------.---------uL-----i-D--x--v--u------PK@@--------------------------_rels/.rels---j---@----Q----N/c------[IL---j-------]-aG----zs-Fu--]---U--------[--x-----1x-p-----f---I---Y--------------D---i----c----qU----3--1--jH[----E--------f---3-----]-T-2-j----l-/---b------z----------/--f--Z-----6--Y-_-o--]A--PK@@--------------------------word/_rels/document.xml.rels--Mo----------P---Q-l--nL---i--I--------------Q--y-x--m-----F--K----MQ-2g------]--h----M----Wh------L---9---G-QV--LM---c[A.---------------5-Y-3------9--R-----Z-tD--c----l-----R---q----D-Y--l2-------------O-----T-------o-GZ6--5U-J-----G-----G-----P------QLy-v--Y---nc-----a--AdnG----V-------------x--2-d-----h--E---Mm------------PK@@--------------------------word/d`

