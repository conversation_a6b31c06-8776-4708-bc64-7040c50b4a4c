#!/usr/bin/env python3
"""
Debug CSV file để hiểu cấu trúc thực tế
"""

import os

def debug_csv():
    csv_path = "D:/InsertData/dulieu.csv"
    
    if not os.path.exists(csv_path):
        print("❌ File không tồn tại")
        return
    
    print(f"📁 File size: {os.path.getsize(csv_path)} bytes")
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        # Đọc 1000 ký tự đầu tiên
        content = f.read(1000)
        print("📄 First 1000 characters:")
        print(repr(content))
        print("\n" + "="*50)
        print("📄 Readable format:")
        print(content)
        
        # Tìm JSON start
        json_start = content.find('[{')
        if json_start != -1:
            print(f"\n✅ Found JSON at position: {json_start}")
            json_part = content[json_start:json_start+200]
            print("📄 JSON part (first 200 chars):")
            print(repr(json_part))
        else:
            print("\n❌ No JSON found in first 1000 chars")
            
            # Tìm các pattern khác
            patterns = ['"', '{', '[', 'bill', 'receiver']
            for pattern in patterns:
                pos = content.find(pattern)
                if pos != -1:
                    print(f"Found '{pattern}' at position: {pos}")

if __name__ == "__main__":
    debug_csv()
