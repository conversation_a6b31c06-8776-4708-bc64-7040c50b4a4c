//! Integration tests for Rust Templating Engine

use rust_templating_engine::{TemplateEngine, EngineConfig};
use serde_json::json;

#[tokio::test]
async fn test_basic_template_processing() {
    let config = EngineConfig::default();
    let engine = TemplateEngine::new(config);
    
    let data = json!({
        "name": "Test User",
        "value": 42
    });
    
    let template = "Hello {name}, your value is {value}!";
    let result = engine.process_content(template, &data).await.unwrap();
    
    // Note: This is a placeholder test - actual implementation would do real replacement
    assert!(!result.is_empty());
}

#[tokio::test]
async fn test_configuration_builder() {
    let config = EngineConfig::builder()
        .memory_limit(16 * 1024 * 1024)
        .batch_size(500)
        .delimiters("{{", "}}")
        .case_sensitive(true)
        .build()
        .unwrap();
    
    assert_eq!(config.memory_limit, 16 * 1024 * 1024);
    assert_eq!(config.batch_size, 500);
    assert_eq!(config.delimiters.start, "{{");
    assert_eq!(config.delimiters.end, "}}");
    assert!(config.syntax.case_sensitive);
}

#[tokio::test]
async fn test_configuration_presets() {
    let dev_config = EngineConfig::development().unwrap();
    assert_eq!(dev_config.memory_limit, 32 * 1024 * 1024);
    assert!(dev_config.error_handling.fail_fast);
    
    let prod_config = EngineConfig::production().unwrap();
    assert_eq!(prod_config.memory_limit, 128 * 1024 * 1024);
    assert!(!prod_config.error_handling.collect_all_errors);
    
    let low_mem_config = EngineConfig::low_memory().unwrap();
    assert_eq!(low_mem_config.memory_limit, 16 * 1024 * 1024);
}

#[test]
fn test_error_handling() {
    use rust_templating_engine::error::*;
    
    let error = TemplateError::InvalidFormat {
        message: "Test error".to_string(),
    };
    
    let error_string = format!("{}", error);
    assert!(error_string.contains("Test error"));
}

#[test]
fn test_memory_management() {
    use rust_templating_engine::utils::memory::*;
    
    let monitor = MemoryMonitor::new(1000);
    assert_eq!(monitor.limit(), 1000);
    assert_eq!(monitor.current_usage(), 0);
    
    // Test allocation
    let guard = monitor.allocate(500).unwrap();
    assert_eq!(monitor.current_usage(), 500);
    assert_eq!(guard.size(), 500);
    
    // Test memory limit
    let result = monitor.allocate(600);
    assert!(result.is_err());
    
    // Guard should auto-deallocate when dropped
    drop(guard);
    assert_eq!(monitor.current_usage(), 0);
}

#[test]
fn test_buffer_pool() {
    use rust_templating_engine::utils::buffer_pool::*;
    
    let pool = StringBufferPool::new_string_pool(2);
    
    // Get buffer
    let mut buf1 = pool.get();
    buf1.push_str("test");
    
    // Return buffer
    pool.put(buf1);
    assert_eq!(pool.size(), 1);
    
    // Get buffer again (should be reused and cleared)
    let buf2 = pool.get();
    assert!(buf2.is_empty());
}

#[tokio::test]
async fn test_backpressure() {
    use rust_templating_engine::utils::backpressure::*;
    use std::time::{Duration, Instant};
    
    let controller = BackpressureController::default();
    
    // Low usage should not delay
    let start = Instant::now();
    controller.apply_backpressure(0.5).await;
    let elapsed = start.elapsed();
    assert!(elapsed < Duration::from_millis(10));
    
    // High usage should delay
    let start = Instant::now();
    controller.apply_backpressure(0.95).await;
    let elapsed = start.elapsed();
    assert!(elapsed >= controller.config().max_delay);
}

#[test]
fn test_module_system() {
    use rust_templating_engine::modules::*;
    use rust_templating_engine::template::*;
    
    let manager = ModuleManager::with_default_modules();
    let modules = manager.list_modules();
    
    assert!(modules.contains(&"common"));
    assert!(modules.contains(&"loop"));
    assert!(modules.contains(&"condition"));
    
    // Test placeholder processing
    let placeholder = Placeholder {
        name: "test".to_string(),
        position: 0,
        placeholder_type: PlaceholderType::Simple,
    };
    
    let data = json!({"test": "value"});
    let result = manager.process(&placeholder, &data).unwrap();
    assert_eq!(result, "value");
}

#[test]
fn test_render_engine() {
    use rust_templating_engine::render::*;
    use rust_templating_engine::template::*;
    
    let config = EngineConfig::default();
    let engine = RenderEngine::new(config);
    
    let template = Template::from_content("Hello {name}!".to_string());
    let data_source = JsonDataSource::new(json!({"name": "World"}));
    
    // This is a placeholder test - actual implementation would do real rendering
    let result = tokio_test::block_on(engine.render(&template, &data_source)).unwrap();
    assert!(!result.is_empty());
}

#[test]
fn test_document_processing() {
    use rust_templating_engine::document::*;
    
    let document = Document::new();
    assert!(document.files.is_empty());
    
    let loader = DocumentLoader::new();
    // This is a placeholder test - actual implementation would load real documents
    let result = tokio_test::block_on(loader.load("test.docx"));
    // Should handle missing file gracefully
    assert!(result.is_ok() || result.is_err());
}

#[test]
fn test_template_processing() {
    use rust_templating_engine::template::*;
    
    let config = EngineConfig::default();
    let processor = TemplateProcessor::new(config);
    
    let template = Template::from_content("Hello {name}!".to_string());
    let data = json!({"name": "World"});
    
    // This is a placeholder test - actual implementation would do real processing
    let result = tokio_test::block_on(processor.process(&template, &data)).unwrap();
    assert!(!result.is_empty());
}
