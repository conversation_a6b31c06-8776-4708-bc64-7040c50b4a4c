//! Backpressure utilities for flow control

use std::time::{Duration, Instant};

/// Backpressure configuration
#[derive(Debug, <PERSON>lone)]
pub struct BackpressureConfig {
    pub soft_threshold: f64,    // 0.7 = 70% usage
    pub hard_threshold: f64,    // 0.9 = 90% usage
    pub min_delay: Duration,
    pub max_delay: Duration,
    pub backoff_multiplier: f64,
}

impl Default for BackpressureConfig {
    fn default() -> Self {
        Self {
            soft_threshold: 0.7,
            hard_threshold: 0.9,
            min_delay: Duration::from_millis(1),
            max_delay: Duration::from_millis(1000),
            backoff_multiplier: 2.0,
        }
    }
}

/// Backpressure controller
#[derive(Debug)]
pub struct BackpressureController {
    config: BackpressureConfig,
    current_delay: std::sync::Mutex<Duration>,
    last_check: std::sync::Mutex<Instant>,
    check_interval: Duration,
}

impl BackpressureController {
    /// Create new backpressure controller
    pub fn new(config: BackpressureConfig) -> Self {
        Self {
            config,
            current_delay: std::sync::Mutex::new(Duration::ZERO),
            last_check: std::sync::Mutex::new(Instant::now()),
            check_interval: Duration::from_millis(100),
        }
    }
    
    /// Create with default configuration
    pub fn default() -> Self {
        Self::new(BackpressureConfig::default())
    }
    
    /// Calculate delay based on usage ratio
    pub fn calculate_delay(&self, usage_ratio: f64) -> Duration {
        if usage_ratio < self.config.soft_threshold {
            Duration::ZERO
        } else if usage_ratio < self.config.hard_threshold {
            // Linear interpolation between min and max delay
            let ratio = (usage_ratio - self.config.soft_threshold) / 
                       (self.config.hard_threshold - self.config.soft_threshold);
            
            let delay_ms = self.config.min_delay.as_millis() as f64 + 
                          ratio * (self.config.max_delay.as_millis() as f64 - 
                                  self.config.min_delay.as_millis() as f64);
            
            Duration::from_millis(delay_ms as u64)
        } else {
            // Above hard threshold, use maximum delay
            self.config.max_delay
        }
    }
    
    /// Apply backpressure delay
    pub async fn apply_backpressure(&self, usage_ratio: f64) {
        let delay = self.calculate_delay(usage_ratio);
        
        if !delay.is_zero() {
            // Update current delay with exponential backoff
            {
                let mut current_delay = self.current_delay.lock().unwrap();
                if delay > *current_delay {
                    *current_delay = delay;
                } else {
                    // Gradually reduce delay
                    *current_delay = Duration::from_millis(
                        (current_delay.as_millis() as f64 / self.config.backoff_multiplier) as u64
                    ).max(delay);
                }
            }
            
            tokio::time::sleep(delay).await;
        } else {
            // Reset delay when usage is low
            *self.current_delay.lock().unwrap() = Duration::ZERO;
        }
    }
    
    /// Check if should apply backpressure
    pub fn should_apply_backpressure(&self, usage_ratio: f64) -> bool {
        usage_ratio >= self.config.soft_threshold
    }
    
    /// Check if should reject new requests
    pub fn should_reject(&self, usage_ratio: f64) -> bool {
        usage_ratio >= self.config.hard_threshold
    }
    
    /// Get current delay
    pub fn current_delay(&self) -> Duration {
        *self.current_delay.lock().unwrap()
    }
    
    /// Get configuration
    pub fn config(&self) -> &BackpressureConfig {
        &self.config
    }

    /// Check if enough time has passed since last check
    pub fn should_check_now(&self) -> bool {
        let now = Instant::now();
        if let Ok(mut last_check) = self.last_check.lock() {
            if now.duration_since(*last_check) >= self.check_interval {
                *last_check = now;
                true
            } else {
                false
            }
        } else {
            true
        }
    }
}

/// Adaptive backpressure controller that adjusts based on system performance
#[derive(Debug)]
pub struct AdaptiveBackpressureController {
    base_controller: BackpressureController,
    performance_history: std::sync::Mutex<Vec<(Instant, f64)>>, // (time, throughput)
    max_history: usize,
    adaptation_factor: f64,
}

impl AdaptiveBackpressureController {
    /// Create new adaptive controller
    pub fn new(config: BackpressureConfig) -> Self {
        Self {
            base_controller: BackpressureController::new(config),
            performance_history: std::sync::Mutex::new(Vec::new()),
            max_history: 100,
            adaptation_factor: 0.1,
        }
    }
    
    /// Record performance metric
    pub fn record_performance(&self, throughput: f64) {
        let mut history = self.performance_history.lock().unwrap();
        history.push((Instant::now(), throughput));
        
        // Keep history bounded
        if history.len() > self.max_history {
            history.remove(0);
        }
    }
    
    /// Get average throughput over recent history
    pub fn average_throughput(&self) -> f64 {
        let history = self.performance_history.lock().unwrap();
        if history.is_empty() {
            0.0
        } else {
            let sum: f64 = history.iter().map(|(_, throughput)| throughput).sum();
            sum / history.len() as f64
        }
    }
    
    /// Apply adaptive backpressure
    pub async fn apply_adaptive_backpressure(&self, usage_ratio: f64, current_throughput: f64) {
        // Record current performance
        self.record_performance(current_throughput);
        
        // Calculate adaptive threshold based on performance trend
        let avg_throughput = self.average_throughput();
        let performance_ratio = if avg_throughput > 0.0 {
            current_throughput / avg_throughput
        } else {
            1.0
        };
        
        // Adjust thresholds based on performance
        let mut adjusted_config = self.base_controller.config().clone();
        
        if performance_ratio < 0.8 {
            // Performance is degrading, be more aggressive with backpressure
            let factor = 1.0 - self.adaptation_factor;
            adjusted_config.soft_threshold *= factor;
            adjusted_config.hard_threshold *= factor;
        } else if performance_ratio > 1.2 {
            // Performance is good, relax backpressure
            let factor = 1.0 + self.adaptation_factor;
            adjusted_config.soft_threshold *= factor;
            adjusted_config.hard_threshold *= factor;
        }
        
        // Ensure thresholds stay within reasonable bounds
        adjusted_config.soft_threshold = adjusted_config.soft_threshold.clamp(0.5, 0.9);
        adjusted_config.hard_threshold = adjusted_config.hard_threshold.clamp(0.7, 0.95);
        
        // Apply backpressure with adjusted config
        let temp_controller = BackpressureController::new(adjusted_config);
        temp_controller.apply_backpressure(usage_ratio).await;
    }
}

/// Backpressure metrics
#[derive(Debug, Clone)]
pub struct BackpressureMetrics {
    pub total_delays: usize,
    pub total_delay_time: Duration,
    pub max_delay: Duration,
    pub current_usage_ratio: f64,
    pub rejections: usize,
}

impl Default for BackpressureMetrics {
    fn default() -> Self {
        Self {
            total_delays: 0,
            total_delay_time: Duration::ZERO,
            max_delay: Duration::ZERO,
            current_usage_ratio: 0.0,
            rejections: 0,
        }
    }
}

/// Backpressure monitor for collecting metrics
#[derive(Debug)]
pub struct BackpressureMonitor {
    metrics: std::sync::Mutex<BackpressureMetrics>,
    controller: BackpressureController,
}

impl BackpressureMonitor {
    /// Create new monitor
    pub fn new(config: BackpressureConfig) -> Self {
        Self {
            metrics: std::sync::Mutex::new(BackpressureMetrics::default()),
            controller: BackpressureController::new(config),
        }
    }
    
    /// Apply backpressure with monitoring
    pub async fn apply_monitored_backpressure(&self, usage_ratio: f64) {
        let delay = self.controller.calculate_delay(usage_ratio);
        
        if !delay.is_zero() {
            // Update metrics
            {
                let mut metrics = self.metrics.lock().unwrap();
                metrics.total_delays += 1;
                metrics.total_delay_time += delay;
                metrics.max_delay = metrics.max_delay.max(delay);
                metrics.current_usage_ratio = usage_ratio;
            }
            
            tokio::time::sleep(delay).await;
        }
        
        // Update current usage ratio
        self.metrics.lock().unwrap().current_usage_ratio = usage_ratio;
    }
    
    /// Record rejection
    pub fn record_rejection(&self) {
        self.metrics.lock().unwrap().rejections += 1;
    }
    
    /// Get current metrics
    pub fn metrics(&self) -> BackpressureMetrics {
        self.metrics.lock().unwrap().clone()
    }
    
    /// Reset metrics
    pub fn reset_metrics(&self) {
        *self.metrics.lock().unwrap() = BackpressureMetrics::default();
    }
    
    /// Get average delay per operation
    pub fn average_delay(&self) -> Duration {
        let metrics = self.metrics.lock().unwrap();
        if metrics.total_delays == 0 {
            Duration::ZERO
        } else {
            metrics.total_delay_time / metrics.total_delays as u32
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_backpressure_config() {
        let config = BackpressureConfig::default();
        assert_eq!(config.soft_threshold, 0.7);
        assert_eq!(config.hard_threshold, 0.9);
    }
    
    #[test]
    fn test_delay_calculation() {
        let controller = BackpressureController::default();
        
        // Below soft threshold - no delay
        assert_eq!(controller.calculate_delay(0.5), Duration::ZERO);
        
        // At soft threshold - minimum delay
        let delay = controller.calculate_delay(0.7);
        assert!(delay >= controller.config.min_delay);
        
        // Above hard threshold - maximum delay
        let delay = controller.calculate_delay(0.95);
        assert_eq!(delay, controller.config.max_delay);
    }
    
    #[tokio::test]
    async fn test_backpressure_application() {
        let controller = BackpressureController::default();
        
        // Low usage - should not delay
        let start = Instant::now();
        controller.apply_backpressure(0.5).await;
        let elapsed = start.elapsed();
        assert!(elapsed < Duration::from_millis(10));
        
        // High usage - should delay
        let start = Instant::now();
        controller.apply_backpressure(0.95).await;
        let elapsed = start.elapsed();
        assert!(elapsed >= controller.config.max_delay);
    }
    
    #[test]
    fn test_adaptive_controller() {
        let controller = AdaptiveBackpressureController::new(BackpressureConfig::default());
        
        // Record some performance data
        controller.record_performance(100.0);
        controller.record_performance(90.0);
        controller.record_performance(110.0);
        
        let avg = controller.average_throughput();
        assert!((avg - 100.0).abs() < 10.0);
    }
    
    #[tokio::test]
    async fn test_backpressure_monitor() {
        let monitor = BackpressureMonitor::new(BackpressureConfig::default());
        
        // Apply backpressure
        monitor.apply_monitored_backpressure(0.8).await;
        
        let metrics = monitor.metrics();
        assert_eq!(metrics.total_delays, 1);
        assert!(metrics.total_delay_time > Duration::ZERO);
        assert_eq!(metrics.current_usage_ratio, 0.8);
        
        // Record rejection
        monitor.record_rejection();
        let metrics = monitor.metrics();
        assert_eq!(metrics.rejections, 1);
    }
}
