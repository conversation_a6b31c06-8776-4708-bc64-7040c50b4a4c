//! Error context and position tracking

use std::fmt;

/// Position information for error reporting
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct Position {
    pub line: usize,
    pub column: usize,
    pub offset: usize,
}

impl Position {
    /// Create a new position
    pub fn new(line: usize, column: usize, offset: usize) -> Self {
        Self { line, column, offset }
    }
    
    /// Create position at start of document
    pub fn start() -> Self {
        Self::new(1, 1, 0)
    }
    
    /// Advance position by one character
    pub fn advance(&mut self, ch: char) {
        if ch == '\n' {
            self.line += 1;
            self.column = 1;
        } else {
            self.column += 1;
        }
        self.offset += ch.len_utf8();
    }
    
    /// Advance position by string
    pub fn advance_str(&mut self, s: &str) {
        for ch in s.chars() {
            self.advance(ch);
        }
    }
}

impl Default for Position {
    fn default() -> Self {
        Self::start()
    }
}

impl fmt::Display for Position {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}:{}", self.line, self.column)
    }
}

/// Error context for detailed error reporting
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub file_path: String,
    pub position: Position,
    pub snippet: String,
    pub message: String,
    pub suggestion: Option<String>,
}

impl ErrorContext {
    /// Create new error context
    pub fn new(
        file_path: String,
        position: Position,
        snippet: String,
        message: String,
    ) -> Self {
        Self {
            file_path,
            position,
            snippet,
            message,
            suggestion: None,
        }
    }
    
    /// Add suggestion to error context
    pub fn with_suggestion(mut self, suggestion: String) -> Self {
        self.suggestion = Some(suggestion);
        self
    }
    
    /// Extract snippet around position from source
    pub fn extract_snippet(source: &str, position: Position, context_lines: usize) -> String {
        let lines: Vec<&str> = source.lines().collect();
        let line_idx = position.line.saturating_sub(1);
        
        let start = line_idx.saturating_sub(context_lines);
        let end = std::cmp::min(line_idx + context_lines + 1, lines.len());
        
        let mut snippet = String::new();
        for (i, line) in lines[start..end].iter().enumerate() {
            let line_num = start + i + 1;
            if line_num == position.line {
                snippet.push_str(&format!("→ {:3} | {}\n", line_num, line));
                // Add pointer to column
                snippet.push_str(&format!("    | {}^\n", " ".repeat(position.column.saturating_sub(1))));
            } else {
                snippet.push_str(&format!("  {:3} | {}\n", line_num, line));
            }
        }
        
        snippet
    }
}

impl fmt::Display for ErrorContext {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        writeln!(f, "Error in file '{}' at {}", self.file_path, self.position)?;
        writeln!(f, "Message: {}", self.message)?;
        
        if !self.snippet.is_empty() {
            writeln!(f, "Context:")?;
            write!(f, "{}", self.snippet)?;
        }
        
        if let Some(suggestion) = &self.suggestion {
            writeln!(f, "Suggestion: {}", suggestion)?;
        }
        
        Ok(())
    }
}

/// Error severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    Warning,
    Error,
    Fatal,
}

impl fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ErrorSeverity::Warning => write!(f, "WARNING"),
            ErrorSeverity::Error => write!(f, "ERROR"),
            ErrorSeverity::Fatal => write!(f, "FATAL"),
        }
    }
}

/// Detailed error information
#[derive(Debug, Clone)]
pub struct ErrorInfo {
    pub severity: ErrorSeverity,
    pub code: Option<String>,
    pub context: ErrorContext,
    pub related_errors: Vec<ErrorInfo>,
}

impl ErrorInfo {
    /// Create new error info
    pub fn new(severity: ErrorSeverity, context: ErrorContext) -> Self {
        Self {
            severity,
            code: None,
            context,
            related_errors: Vec::new(),
        }
    }
    
    /// Add error code
    pub fn with_code(mut self, code: String) -> Self {
        self.code = Some(code);
        self
    }
    
    /// Add related error
    pub fn with_related(mut self, error: ErrorInfo) -> Self {
        self.related_errors.push(error);
        self
    }
}

impl fmt::Display for ErrorInfo {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[{}]", self.severity)?;
        
        if let Some(code) = &self.code {
            write!(f, " {}", code)?;
        }
        
        writeln!(f)?;
        write!(f, "{}", self.context)?;
        
        if !self.related_errors.is_empty() {
            writeln!(f, "Related errors:")?;
            for (i, error) in self.related_errors.iter().enumerate() {
                writeln!(f, "  {}. {}", i + 1, error)?;
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_position_advance() {
        let mut pos = Position::start();
        assert_eq!(pos.line, 1);
        assert_eq!(pos.column, 1);
        assert_eq!(pos.offset, 0);
        
        pos.advance('a');
        assert_eq!(pos.line, 1);
        assert_eq!(pos.column, 2);
        assert_eq!(pos.offset, 1);
        
        pos.advance('\n');
        assert_eq!(pos.line, 2);
        assert_eq!(pos.column, 1);
        assert_eq!(pos.offset, 2);
    }
    
    #[test]
    fn test_position_advance_str() {
        let mut pos = Position::start();
        pos.advance_str("hello\nworld");
        
        assert_eq!(pos.line, 2);
        assert_eq!(pos.column, 6);
        assert_eq!(pos.offset, 11);
    }
    
    #[test]
    fn test_extract_snippet() {
        let source = "line 1\nline 2\nline 3\nline 4\nline 5";
        let position = Position::new(3, 5, 14);
        
        let snippet = ErrorContext::extract_snippet(source, position, 1);
        assert!(snippet.contains("line 2"));
        assert!(snippet.contains("→   3 | line 3"));
        assert!(snippet.contains("line 4"));
        assert!(snippet.contains("^"));
    }
    
    #[test]
    fn test_error_context_display() {
        let context = ErrorContext::new(
            "test.txt".to_string(),
            Position::new(5, 10, 50),
            "sample snippet".to_string(),
            "Test error message".to_string(),
        ).with_suggestion("Try this instead".to_string());
        
        let display = format!("{}", context);
        assert!(display.contains("test.txt"));
        assert!(display.contains("5:10"));
        assert!(display.contains("Test error message"));
        assert!(display.contains("Try this instead"));
    }
}
