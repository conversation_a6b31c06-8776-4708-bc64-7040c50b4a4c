//! Semaphore helpers for concurrency control

use std::sync::Arc;
use tokio::sync::{Semaphore, SemaphorePermit};
use std::time::Duration;

/// Semaphore wrapper with additional utilities
#[derive(Debug, Clone)]
pub struct SemaphoreHelper {
    semaphore: Arc<Semaphore>,
    max_permits: usize,
}

impl SemaphoreHelper {
    /// Create new semaphore helper
    pub fn new(permits: usize) -> Self {
        Self {
            semaphore: Arc::new(Semaphore::new(permits)),
            max_permits: permits,
        }
    }
    
    /// Acquire permit
    pub async fn acquire(&self) -> Result<SemaphorePermit<'_>, tokio::sync::AcquireError> {
        self.semaphore.acquire().await
    }
    
    /// Try to acquire permit (non-blocking)
    pub fn try_acquire(&self) -> Result<SemaphorePermit<'_>, tokio::sync::TryAcquireError> {
        self.semaphore.try_acquire()
    }
    
    /// Acquire permit with timeout
    pub async fn acquire_timeout(&self, timeout: Duration) -> Result<SemaphorePermit<'_>, AcquireTimeoutError> {
        match tokio::time::timeout(timeout, self.semaphore.acquire()).await {
            Ok(Ok(permit)) => Ok(permit),
            Ok(Err(e)) => Err(AcquireTimeoutError::Acquire(e)),
            Err(_) => Err(AcquireTimeoutError::Timeout),
        }
    }
    
    /// Acquire multiple permits
    pub async fn acquire_many(&self, n: u32) -> Result<SemaphorePermit<'_>, tokio::sync::AcquireError> {
        self.semaphore.acquire_many(n).await
    }
    
    /// Try to acquire multiple permits
    pub fn try_acquire_many(&self, n: u32) -> Result<SemaphorePermit<'_>, tokio::sync::TryAcquireError> {
        self.semaphore.try_acquire_many(n)
    }
    
    /// Get available permits
    pub fn available_permits(&self) -> usize {
        self.semaphore.available_permits()
    }
    
    /// Get maximum permits
    pub fn max_permits(&self) -> usize {
        self.max_permits
    }
    
    /// Get usage ratio (0.0 to 1.0)
    pub fn usage_ratio(&self) -> f64 {
        let available = self.available_permits();
        let used = self.max_permits.saturating_sub(available);
        used as f64 / self.max_permits as f64
    }
    
    /// Check if semaphore is at capacity
    pub fn is_at_capacity(&self) -> bool {
        self.available_permits() == 0
    }
    
    /// Add permits to semaphore
    pub fn add_permits(&self, n: usize) {
        self.semaphore.add_permits(n);
    }
    
    /// Close semaphore (no more permits can be acquired)
    pub fn close(&self) {
        self.semaphore.close();
    }
    
    /// Check if semaphore is closed
    pub fn is_closed(&self) -> bool {
        self.semaphore.is_closed()
    }
}

/// Acquire timeout error
#[derive(Debug)]
pub enum AcquireTimeoutError {
    Timeout,
    Acquire(tokio::sync::AcquireError),
}

impl std::fmt::Display for AcquireTimeoutError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AcquireTimeoutError::Timeout => write!(f, "Acquire timeout"),
            AcquireTimeoutError::Acquire(e) => write!(f, "Acquire error: {}", e),
        }
    }
}

impl std::error::Error for AcquireTimeoutError {}

/// Semaphore pool for managing multiple semaphores
#[derive(Debug)]
pub struct SemaphorePool {
    semaphores: Vec<SemaphoreHelper>,
    round_robin_index: std::sync::atomic::AtomicUsize,
}

impl SemaphorePool {
    /// Create new semaphore pool
    pub fn new(count: usize, permits_per_semaphore: usize) -> Self {
        let semaphores = (0..count)
            .map(|_| SemaphoreHelper::new(permits_per_semaphore))
            .collect();
        
        Self {
            semaphores,
            round_robin_index: std::sync::atomic::AtomicUsize::new(0),
        }
    }
    
    /// Get next semaphore using round-robin
    pub fn next_semaphore(&self) -> &SemaphoreHelper {
        let index = self.round_robin_index
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed) % self.semaphores.len();
        &self.semaphores[index]
    }
    
    /// Get semaphore with most available permits
    pub fn least_used_semaphore(&self) -> &SemaphoreHelper {
        self.semaphores
            .iter()
            .max_by_key(|s| s.available_permits())
            .unwrap_or(&self.semaphores[0])
    }
    
    /// Try to acquire from any semaphore
    pub fn try_acquire_any(&self) -> Option<(usize, SemaphorePermit<'_>)> {
        for (index, semaphore) in self.semaphores.iter().enumerate() {
            if let Ok(permit) = semaphore.try_acquire() {
                return Some((index, permit));
            }
        }
        None
    }
    
    /// Get total available permits across all semaphores
    pub fn total_available_permits(&self) -> usize {
        self.semaphores.iter().map(|s| s.available_permits()).sum()
    }
    
    /// Get total maximum permits
    pub fn total_max_permits(&self) -> usize {
        self.semaphores.iter().map(|s| s.max_permits()).sum()
    }
    
    /// Get overall usage ratio
    pub fn overall_usage_ratio(&self) -> f64 {
        let total_max = self.total_max_permits();
        if total_max == 0 {
            0.0
        } else {
            let total_available = self.total_available_permits();
            let total_used = total_max.saturating_sub(total_available);
            total_used as f64 / total_max as f64
        }
    }
    
    /// Get semaphore count
    pub fn len(&self) -> usize {
        self.semaphores.len()
    }
    
    /// Check if pool is empty
    pub fn is_empty(&self) -> bool {
        self.semaphores.is_empty()
    }
}

/// Weighted semaphore for different priority levels
#[derive(Debug)]
pub struct WeightedSemaphore {
    high_priority: SemaphoreHelper,
    normal_priority: SemaphoreHelper,
    low_priority: SemaphoreHelper,
}

impl WeightedSemaphore {
    /// Create new weighted semaphore
    pub fn new(high_permits: usize, normal_permits: usize, low_permits: usize) -> Self {
        Self {
            high_priority: SemaphoreHelper::new(high_permits),
            normal_priority: SemaphoreHelper::new(normal_permits),
            low_priority: SemaphoreHelper::new(low_permits),
        }
    }
    
    /// Acquire high priority permit
    pub async fn acquire_high(&self) -> Result<SemaphorePermit<'_>, tokio::sync::AcquireError> {
        self.high_priority.acquire().await
    }
    
    /// Acquire normal priority permit
    pub async fn acquire_normal(&self) -> Result<SemaphorePermit<'_>, tokio::sync::AcquireError> {
        self.normal_priority.acquire().await
    }
    
    /// Acquire low priority permit
    pub async fn acquire_low(&self) -> Result<SemaphorePermit<'_>, tokio::sync::AcquireError> {
        self.low_priority.acquire().await
    }
    
    /// Try to acquire any available permit (high priority first)
    pub fn try_acquire_any(&self) -> Option<SemaphorePermit<'_>> {
        self.high_priority.try_acquire().ok()
            .or_else(|| self.normal_priority.try_acquire().ok())
            .or_else(|| self.low_priority.try_acquire().ok())
    }
    
    /// Get statistics for all priority levels
    pub fn stats(&self) -> WeightedSemaphoreStats {
        WeightedSemaphoreStats {
            high_available: self.high_priority.available_permits(),
            high_max: self.high_priority.max_permits(),
            normal_available: self.normal_priority.available_permits(),
            normal_max: self.normal_priority.max_permits(),
            low_available: self.low_priority.available_permits(),
            low_max: self.low_priority.max_permits(),
        }
    }
}

/// Weighted semaphore statistics
#[derive(Debug, Clone)]
pub struct WeightedSemaphoreStats {
    pub high_available: usize,
    pub high_max: usize,
    pub normal_available: usize,
    pub normal_max: usize,
    pub low_available: usize,
    pub low_max: usize,
}

impl WeightedSemaphoreStats {
    /// Get total available permits
    pub fn total_available(&self) -> usize {
        self.high_available + self.normal_available + self.low_available
    }
    
    /// Get total maximum permits
    pub fn total_max(&self) -> usize {
        self.high_max + self.normal_max + self.low_max
    }
    
    /// Get overall usage ratio
    pub fn usage_ratio(&self) -> f64 {
        let total_max = self.total_max();
        if total_max == 0 {
            0.0
        } else {
            let total_used = total_max.saturating_sub(self.total_available());
            total_used as f64 / total_max as f64
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_semaphore_helper() {
        let helper = SemaphoreHelper::new(2);
        
        assert_eq!(helper.available_permits(), 2);
        assert_eq!(helper.max_permits(), 2);
        assert!(!helper.is_at_capacity());
        
        let _permit1 = helper.acquire().await.unwrap();
        assert_eq!(helper.available_permits(), 1);
        
        let _permit2 = helper.acquire().await.unwrap();
        assert_eq!(helper.available_permits(), 0);
        assert!(helper.is_at_capacity());
        
        // Should not be able to acquire more
        assert!(helper.try_acquire().is_err());
    }
    
    #[tokio::test]
    async fn test_semaphore_pool() {
        let pool = SemaphorePool::new(3, 2);
        
        assert_eq!(pool.len(), 3);
        assert_eq!(pool.total_max_permits(), 6);
        assert_eq!(pool.total_available_permits(), 6);
        
        // Acquire from different semaphores
        let (index1, _permit1) = pool.try_acquire_any().unwrap();
        let (index2, _permit2) = pool.try_acquire_any().unwrap();
        
        // Should be able to acquire from different semaphores
        assert_eq!(pool.total_available_permits(), 4);
    }
    
    #[tokio::test]
    async fn test_weighted_semaphore() {
        let weighted = WeightedSemaphore::new(1, 2, 3);
        
        let stats = weighted.stats();
        assert_eq!(stats.high_available, 1);
        assert_eq!(stats.normal_available, 2);
        assert_eq!(stats.low_available, 3);
        assert_eq!(stats.total_available(), 6);
        
        // Acquire high priority
        let _high_permit = weighted.acquire_high().await.unwrap();
        
        let stats = weighted.stats();
        assert_eq!(stats.high_available, 0);
        assert_eq!(stats.total_available(), 5);
    }
}
