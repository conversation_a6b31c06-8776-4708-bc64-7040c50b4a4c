{"rustc": 12200708597866198150, "features": "[\"alloc\", \"fs\", \"libc-extra-traits\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 1442335954752063332, "path": 2244960038917290693, "deps": [[3430646239657634944, "build_script_build", false, 5008430719172070514], [7896293946984509699, "bitflags", false, 13891028335121853008], [8253628577145923712, "libc_errno", false, 423907319981845271], [10281541584571964250, "windows_sys", false, 12039089208501270321]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustix-7b36b4b481d7fee8\\dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}