{"$message_type":"diagnostic","message":"this method takes 1 argument but 2 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"tests\\integration_test.rs","byte_start":5752,"byte_end":5761,"line_start":192,"line_end":192,"column_start":57,"column_end":66,"is_primary":false,"text":[{"text":"    let result = tokio_test::block_on(processor.process(&template, &data)).unwrap();","highlight_start":57,"highlight_end":66}],"label":"expected `&str`, found `&Template`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\integration_test.rs","byte_start":5763,"byte_end":5768,"line_start":192,"line_end":192,"column_start":68,"column_end":73,"is_primary":false,"text":[{"text":"    let result = tokio_test::block_on(processor.process(&template, &data)).unwrap();","highlight_start":68,"highlight_end":73}],"label":"unexpected argument #2 of type `&Value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\integration_test.rs","byte_start":5744,"byte_end":5751,"line_start":192,"line_end":192,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"    let result = tokio_test::block_on(processor.process(&template, &data)).unwrap();","highlight_start":49,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected reference `&str`\n   found reference `&rust_templating_engine::template::Template`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"D:\\InsertData\\rust-templating-engine\\src\\template\\mod.rs","byte_start":2431,"byte_end":2438,"line_start":102,"line_end":102,"column_start":18,"column_end":25,"is_primary":true,"text":[{"text":"    pub async fn process(&self, content: &str) -> Result<Template> {","highlight_start":18,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra argument","code":null,"level":"help","spans":[{"file_name":"tests\\integration_test.rs","byte_start":5761,"byte_end":5768,"line_start":192,"line_end":192,"column_start":66,"column_end":73,"is_primary":true,"text":[{"text":"    let result = tokio_test::block_on(processor.process(&template, &data)).unwrap();","highlight_start":66,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"tests\\integration_test.rs","byte_start":5752,"byte_end":5761,"line_start":192,"line_end":192,"column_start":57,"column_end":66,"is_primary":true,"text":[{"text":"    let result = tokio_test::block_on(processor.process(&template, &data)).unwrap();","highlight_start":57,"highlight_end":66}],"label":null,"suggested_replacement":"/* &str */","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: this method takes 1 argument but 2 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\integration_test.rs:192:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let result = tokio_test::block_on(processor.process(&template, &data)).unwrap();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #2 of type `&Value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mexpected `&str`, found `&Template`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35mstr\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35mrust_templating_engine::template::Template\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mD:\\InsertData\\rust-templating-engine\\src\\template\\mod.rs:102:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn process(&self, content: &str) -> Result<Template> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let result = tokio_test::block_on(processor.process(\u001b[0m\u001b[0m\u001b[38;5;9m&template, &data\u001b[0m\u001b[0m)).unwrap();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let result = tokio_test::block_on(processor.process(\u001b[0m\u001b[0m\u001b[38;5;10m/* &str */\u001b[0m\u001b[0m)).unwrap();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `is_empty` found for struct `rust_templating_engine::template::Template` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"tests\\integration_test.rs","byte_start":5801,"byte_end":5809,"line_start":193,"line_end":193,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"    assert!(!result.is_empty());","highlight_start":21,"highlight_end":29}],"label":"method not found in `Template`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"some of the expressions' fields have a method of the same name","code":null,"level":"help","spans":[{"file_name":"tests\\integration_test.rs","byte_start":5801,"byte_end":5801,"line_start":193,"line_end":193,"column_start":21,"column_end":21,"is_primary":true,"text":[{"text":"    assert!(!result.is_empty());","highlight_start":21,"highlight_end":21}],"label":null,"suggested_replacement":"content.","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests\\integration_test.rs","byte_start":5801,"byte_end":5801,"line_start":193,"line_end":193,"column_start":21,"column_end":21,"is_primary":true,"text":[{"text":"    assert!(!result.is_empty());","highlight_start":21,"highlight_end":21}],"label":null,"suggested_replacement":"placeholders.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `is_empty` found for struct `rust_templating_engine::template::Template` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\integration_test.rs:193:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    assert!(!result.is_empty());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Template`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: some of the expressions' fields have a method of the same name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    assert!(!result.\u001b[0m\u001b[0m\u001b[38;5;10mcontent.\u001b[0m\u001b[0mis_empty());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[38;5;10m++++++++\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    assert!(!result.\u001b[0m\u001b[0m\u001b[38;5;10mplaceholders.\u001b[0m\u001b[0mis_empty());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 2 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0061, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
