{"rustc": 12200708597866198150, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 13979323166205918175, "deps": [[1988483478007900009, "unicode_ident", false, 15679638612468251457], [3060637413840920116, "proc_macro2", false, 6090349418290637220], [17990358020177143287, "quote", false, 6430127862117674003]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-d870c1dbb6e99c79\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}