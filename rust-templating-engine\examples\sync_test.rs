use rust_templating_engine::{EngineConfig, Result};
use serde_json::json;

/// Sync test để verify engine hoạt động mà không cần async
fn main() -> Result<()> {
    println!("🚀 SYNC ENGINE TEST");
    println!("=" .repeat(30));
    
    // Tạo config
    let config = EngineConfig::builder()
        .memory_limit(64 * 1024 * 1024) // 64MB
        .delimiters("{", "}")
        .build()?;
    
    println!("✅ Config created successfully");
    println!("📊 Memory limit: {} MB", config.memory_limit / (1024 * 1024));
    println!("📊 Delimiters: '{}' and '{}'", config.delimiters.0, config.delimiters.1);
    
    // Test data giống shipping record
    let test_data = json!({
        "bill": "TW00123456",
        "receiver": "Test Company Ltd.",
        "shpper": "Sender Corp",
        "created": "2024-01-15T10:30:00.000",
        "weight": 2.5
    });
    
    println!("\n📝 Test data created:");
    println!("{}", serde_json::to_string_pretty(&test_data).unwrap());
    
    // Simple template processing (manual)
    let template = "Bill: {bill}\nReceiver: {receiver}\nWeight: {weight}kg";
    println!("\n📄 Template:");
    println!("{}", template);
    
    // Manual template processing để test logic
    let mut result = template.to_string();
    if let Some(obj) = test_data.as_object() {
        for (key, value) in obj {
            let placeholder = format!("{{{}}}", key);
            let replacement = match value {
                serde_json::Value::String(s) => s.clone(),
                serde_json::Value::Number(n) => n.to_string(),
                _ => value.to_string(),
            };
            result = result.replace(&placeholder, &replacement);
        }
    }
    
    println!("\n✅ Manual processing result:");
    println!("{}", result);
    
    println!("\n🎉 Sync test completed!");
    println!("✅ Config system working");
    println!("✅ JSON data parsing working");
    println!("✅ Template logic working");
    println!("✅ Ready for async integration");
    
    Ok(())
}
