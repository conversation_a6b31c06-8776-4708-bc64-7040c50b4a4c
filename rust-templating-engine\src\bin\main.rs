use rust_templating_engine::{TemplateEngine, EngineConfig};
use rust_templating_engine::excel::CsvExporter;
use serde_json::{json, Value};
use std::fs;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 RUST TEMPLATING ENGINE - MAIN PROGRAM");
    println!("{}", "=".repeat(50));
    
    // 1. Tạo engine với cấu hình
    let config = EngineConfig::builder()
        .memory_limit(128 * 1024 * 1024) // 128MB
        .delimiters("{", "}")
        .build()?;
    
    let engine = TemplateEngine::new(config);
    println!("✅ Engine initialized successfully");
    
    // 2. Test với dữ liệu đơn giản
    println!("\n📝 Testing simple template...");
    
    let simple_data = json!({
        "name": "<PERSON>",
        "age": 30,
        "city": "Ho Chi Minh City"
    });
    
    let simple_template = "Hello {name}! You are {age} years old and live in {city}.";
    
    let result = engine.process_content(simple_template, &simple_data).await?;
    println!("✅ Simple template result:");
    println!("{}", result);
    
    // 3. Test với shipping data format
    println!("\n📦 Testing shipping template...");
    
    let shipping_data = json!({
        "bill": "TW00123456",
        "receiver": "ABC Company Ltd.",
        "shpper": "XYZ Logistics Corp",
        "created": "2024-01-15T10:30:00.000",
        "weight": 15.5
    });
    
    let shipping_template = r#"
=== INTERNATIONAL SHIPPING DOCUMENT ===
Bill Number: {bill}
Date: {created}
Shipper: {shpper}
Receiver: {receiver}
Weight: {weight} kg
Status: Ready for delivery
======================================="#;
    
    let shipping_result = engine.process_content(shipping_template, &shipping_data).await?;
    println!("✅ Shipping template result:");
    println!("{}", shipping_result);
    
    // 4. Tích hợp MNF.xlsx với dulieu.csv
    println!("\n📄 INTEGRATING MNF.xlsx TEMPLATE WITH dulieu.csv DATA");
    println!("{}", "=".repeat(60));

    let csv_path = "D:/InsertData/dulieu.csv";
    let xlsx_path = "D:/InsertData/MNF (1).xlsx";

    // Load shipping data từ CSV
    let shipping_records = if std::path::Path::new(csv_path).exists() {
        println!("📁 Loading shipping data from CSV...");

        let csv_content = fs::read_to_string(csv_path)?;

        // Extract JSON từ CSV
        if let Some(json_start) = csv_content.find("[{") {
            let json_part = &csv_content[json_start..];

            // Tìm end của JSON array (safe char boundary)
            let mut bracket_count = 0;
            let mut json_end_chars = 0;
            for (i, char) in json_part.chars().enumerate() {
                match char {
                    '[' => bracket_count += 1,
                    ']' => {
                        bracket_count -= 1;
                        if bracket_count == 0 {
                            json_end_chars = i + 1;
                            break;
                        }
                    }
                    _ => {}
                }
            }

            if json_end_chars > 0 {
                // Convert char index to byte index safely
                let mut json_str: String = json_part.chars().take(json_end_chars).collect();

                // Fix CSV escaped quotes: "" -> "
                json_str = json_str.replace("\"\"", "\"");

                match serde_json::from_str::<Vec<Value>>(&json_str) {
                    Ok(records) => {
                        println!("✅ Loaded {} shipping records from CSV", records.len());
                        Some(records)
                    }
                    Err(e) => {
                        println!("❌ Failed to parse JSON: {}", e);
                        None
                    }
                }
            } else {
                println!("❌ Could not find complete JSON in CSV");
                None
            }
        } else {
            println!("❌ No JSON data found in CSV");
            None
        }
    } else {
        println!("⚠️  CSV file not found at {}", csv_path);
        None
    };

    // Create manifest template based on MNF.xlsx structure
    if std::path::Path::new(xlsx_path).exists() {
        println!("📁 Found MNF.xlsx template file");

        // Create Express Manifest template based on XLSX structure
        let manifest_template = r#"
╔══════════════════════════════════════════════════════════════════════════════╗
║                           LITACO EXPRESS MANIFEST                            ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ Agent/Shipper: LITACO EXPRESS                                               ║
║ Address: 179 DAO DUY ANH, P9, PHU NHUAN, TP HCM                            ║
║ Date: {created}                                                             ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ SHIPPING DETAILS:                                                           ║
║                                                                              ║
║ Bill Number: {bill}                                                         ║
║ Shipper: {shpper}                                                           ║
║ Receiver: {receiver}                                                        ║
║ Weight: {weight} kg                                                         ║
║                                                                              ║
║ Status: READY FOR EXPRESS DELIVERY                                          ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ MANIFEST SECTIONS:                                                          ║
║ • Header: Company information and date                                      ║
║ • Detail: Shipping information and package details                         ║
║ • Footer: Status and processing information                                 ║
╚══════════════════════════════════════════════════════════════════════════════╝"#;

        if let Some(records) = shipping_records {
            println!("\n🚀 Generating Express Manifests...");

            // Generate manifests cho 5 records đầu tiên
            for (i, record) in records.iter().take(5).enumerate() {
                println!("\n--- MANIFEST {} ---", i + 1);

                let manifest_result = engine.process_content(manifest_template, record).await?;
                println!("{}", manifest_result);

                // Thêm separator
                if i < 4 {
                    println!("\n{}", "─".repeat(80));
                }
            }

            // Performance test với manifest template
            println!("\n⚡ PERFORMANCE TEST - Generating 50 manifests...");
            let start_time = std::time::Instant::now();

            let mut generated_manifests = Vec::new();
            for record in records.iter().take(50) {
                let manifest = engine.process_content(manifest_template, record).await?;
                generated_manifests.push(manifest);
            }

            let duration = start_time.elapsed();
            println!("✅ Generated {} manifests in {:?}", generated_manifests.len(), duration);
            println!("📊 Average time per manifest: {:?}", duration / 50);
            println!("📊 Throughput: {:.2} manifests/second", 50.0 / duration.as_secs_f64());

            // Statistics
            let total_chars: usize = generated_manifests.iter().map(|m| m.len()).sum();
            let avg_chars = total_chars / generated_manifests.len();
            println!("📊 Average manifest size: {} characters", avg_chars);

            // Test với records chứa chữ "i"
            println!("\n🔍 FILTERING RECORDS WITH 'i'...");
            let records_with_i: Vec<&Value> = records.iter()
                .filter(|record| {
                    record.as_object().unwrap().values().any(|v| {
                        v.as_str().map_or(false, |s| s.to_lowercase().contains('i'))
                    })
                })
                .take(3)
                .collect();

            println!("📊 Found {} records containing 'i' (showing first 3)", records_with_i.len());

            for (i, record) in records_with_i.iter().enumerate() {
                println!("\n--- FILTERED MANIFEST {} ---", i + 1);
                let manifest = engine.process_content(manifest_template, record).await?;
                println!("{}", manifest);
            }

            // 📊 EXPORT TO CSV
            println!("\n📊 EXPORTING DATA TO CSV...");
            let csv_output_dir = "D:/InsertData/csv_export";

            match CsvExporter::new(csv_output_dir) {
                Ok(exporter) => {
                    let export_start = std::time::Instant::now();

                    // Export first 1000 records to Excel (for performance)
                    let export_records: Vec<&Value> = records.iter().take(1000).collect();
                    let export_records_owned: Vec<Value> = export_records.into_iter().cloned().collect();

                    // Export shipping data
                    match exporter.export_shipping_data(&export_records_owned) {
                        Ok(csv_path) => {
                            println!("✅ Exported {} shipping records to CSV", export_records_owned.len());
                            println!("📁 File: {}", csv_path);
                        }
                        Err(e) => {
                            println!("❌ Failed to export shipping data: {}", e);
                        }
                    }

                    // Export manifests (first 10)
                    let manifest_data: Vec<(Value, String)> = records.iter()
                        .take(10)
                        .map(|record| {
                            // Generate manifest for this record
                            let manifest = format!(
                                "LITACO EXPRESS MANIFEST\nBill: {}\nReceiver: {}\nShipper: {}\nWeight: {} kg\nDate: {}",
                                record.get("bill").and_then(|v| v.as_str()).unwrap_or("N/A"),
                                record.get("receiver").and_then(|v| v.as_str()).unwrap_or("N/A"),
                                record.get("shpper").and_then(|v| v.as_str()).unwrap_or("N/A"),
                                record.get("weight").and_then(|v| v.as_f64()).unwrap_or(0.0),
                                record.get("created").and_then(|v| v.as_str()).unwrap_or("N/A")
                            );
                            (record.clone(), manifest)
                        })
                        .collect();

                    match exporter.export_manifest_data(&manifest_data) {
                        Ok(csv_path) => {
                            println!("✅ Exported {} manifests to CSV", manifest_data.len());
                            println!("📁 File: {}", csv_path);
                        }
                        Err(e) => {
                            println!("❌ Failed to export manifest data: {}", e);
                        }
                    }

                    // Export summary
                    let export_duration = export_start.elapsed();
                    let export_throughput = export_records_owned.len() as f64 / export_duration.as_secs_f64();

                    match exporter.export_summary(export_records_owned.len(), export_duration, export_throughput) {
                        Ok(csv_path) => {
                            println!("✅ Exported summary statistics");
                            println!("📁 File: {}", csv_path);
                        }
                        Err(e) => {
                            println!("❌ Failed to export summary: {}", e);
                        }
                    }

                    println!("✅ CSV export completed successfully!");
                    println!("📁 Export directory: {}", csv_output_dir);
                    println!("📊 Total export time: {:?}", export_start.elapsed());
                }
                Err(e) => {
                    println!("❌ Failed to create CSV exporter: {}", e);
                }
            }

        } else {
            println!("⚠️  No shipping data available for manifest generation");
        }
    } else {
        println!("⚠️  MNF.xlsx file not found at {}", xlsx_path);
    }
    
    // 5. Kết luận
    println!("\n🎉 TESTING COMPLETED!");
    println!("{}", "=".repeat(50));
    println!("✅ Engine is working correctly");
    println!("✅ Template processing successful");
    println!("✅ Data integration working");
    println!("✅ Ready for production use");
    
    Ok(())
}
