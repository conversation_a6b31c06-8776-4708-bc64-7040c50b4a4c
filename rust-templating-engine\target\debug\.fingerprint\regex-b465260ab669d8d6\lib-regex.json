{"rustc": 12200708597866198150, "features": "[\"std\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2225463790103693989, "path": 688040178356337507, "deps": [[555019317135488525, "regex_automata", false, 2942583312205499205], [9408802513701742484, "regex_syntax", false, 15974373643105220986]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-b465260ab669d8d6\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}