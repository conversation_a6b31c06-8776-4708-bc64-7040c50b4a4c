#!/usr/bin/env python3
"""
Chương trình phân tích file dữ liệu CSV và XLSX
Sử dụng pandas để đọc và hiển thị thông tin chi tiết về cấu trúc dữ liệu
"""

import pandas as pd
import os
import sys
from pathlib import Path

def analyze_csv_file(file_path):
    """Phân tích file CSV"""
    print(f"\n{'='*60}")
    print(f"📄 PHÂN TÍCH FILE CSV: {file_path}")
    print(f"{'='*60}")
    
    try:
        # Đọc file CSV
        df = pd.read_csv(file_path, encoding='utf-8')
        
        # Thông tin cơ bản
        print(f"📊 Thông tin cơ bản:")
        print(f"   - Số hàng: {len(df)}")
        print(f"   - Số cột: {len(df.columns)}")
        print(f"   - <PERSON><PERSON><PERSON> thước file: {os.path.getsize(file_path)} bytes")
        
        # Hi<PERSON>n thị tên cột
        print(f"\n📋 Tên các cột:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. '{col}'")
        
        # Hiển thị kiểu dữ liệu
        print(f"\n🔍 Kiểu dữ liệu:")
        for col in df.columns:
            print(f"   - {col}: {df[col].dtype}")
        
        # Hiển thị 10 hàng đầu
        print(f"\n📝 10 hàng đầu tiên:")
        print(df.head(10).to_string(index=True))
        
        # Hiển thị 5 hàng cuối
        if len(df) > 10:
            print(f"\n📝 5 hàng cuối cùng:")
            print(df.tail(5).to_string(index=True))
        
        # Thống kê mô tả
        print(f"\n📈 Thống kê mô tả:")
        print(df.describe(include='all').to_string())
        
        # Kiểm tra dữ liệu chứa chữ "i"
        print(f"\n🔎 Kiểm tra dữ liệu chứa chữ 'i':")
        for col in df.columns:
            if df[col].dtype == 'object':  # Chỉ kiểm tra cột text
                contains_i = df[col].astype(str).str.contains('i', case=False, na=False)
                count_with_i = contains_i.sum()
                print(f"   - Cột '{col}': {count_with_i} hàng chứa chữ 'i'")
                
                if count_with_i > 0:
                    print(f"     Ví dụ: {df[contains_i][col].head(3).tolist()}")
        
        # Kiểm tra giá trị null
        print(f"\n❌ Giá trị null/missing:")
        null_counts = df.isnull().sum()
        for col in df.columns:
            if null_counts[col] > 0:
                print(f"   - {col}: {null_counts[col]} giá trị null")
        
        return df
        
    except Exception as e:
        print(f"❌ Lỗi khi đọc file CSV: {e}")
        return None

def analyze_xlsx_file(file_path):
    """Phân tích file XLSX"""
    print(f"\n{'='*60}")
    print(f"📄 PHÂN TÍCH FILE XLSX: {file_path}")
    print(f"{'='*60}")
    
    try:
        # Đọc tất cả sheets
        excel_file = pd.ExcelFile(file_path)
        print(f"📊 Số lượng sheets: {len(excel_file.sheet_names)}")
        print(f"📋 Tên các sheets: {excel_file.sheet_names}")
        
        all_sheets = {}
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n{'─'*40}")
            print(f"📄 SHEET: '{sheet_name}'")
            print(f"{'─'*40}")
            
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                all_sheets[sheet_name] = df
                
                # Thông tin cơ bản
                print(f"📊 Thông tin cơ bản:")
                print(f"   - Số hàng: {len(df)}")
                print(f"   - Số cột: {len(df.columns)}")
                
                # Hiển thị tên cột
                print(f"\n📋 Tên các cột:")
                for i, col in enumerate(df.columns, 1):
                    print(f"   {i}. '{col}'")
                
                # Hiển thị kiểu dữ liệu
                print(f"\n🔍 Kiểu dữ liệu:")
                for col in df.columns:
                    print(f"   - {col}: {df[col].dtype}")
                
                # Hiển thị 5 hàng đầu
                print(f"\n📝 5 hàng đầu tiên:")
                print(df.head(5).to_string(index=True))
                
                # Thống kê mô tả cho cột số
                numeric_cols = df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    print(f"\n📈 Thống kê cho cột số:")
                    print(df[numeric_cols].describe().to_string())
                
                # Kiểm tra dữ liệu chứa chữ "i"
                print(f"\n🔎 Kiểm tra dữ liệu chứa chữ 'i':")
                for col in df.columns:
                    if df[col].dtype == 'object':  # Chỉ kiểm tra cột text
                        contains_i = df[col].astype(str).str.contains('i', case=False, na=False)
                        count_with_i = contains_i.sum()
                        print(f"   - Cột '{col}': {count_with_i} hàng chứa chữ 'i'")
                        
                        if count_with_i > 0:
                            print(f"     Ví dụ: {df[contains_i][col].head(3).tolist()}")
                
                # Kiểm tra giá trị null
                print(f"\n❌ Giá trị null/missing:")
                null_counts = df.isnull().sum()
                for col in df.columns:
                    if null_counts[col] > 0:
                        print(f"   - {col}: {null_counts[col]} giá trị null")
                
            except Exception as e:
                print(f"❌ Lỗi khi đọc sheet '{sheet_name}': {e}")
        
        return all_sheets
        
    except Exception as e:
        print(f"❌ Lỗi khi đọc file XLSX: {e}")
        return None

def analyze_for_templating_engine(csv_df, xlsx_data):
    """Phân tích khả năng sử dụng với Rust templating engine"""
    print(f"\n{'='*60}")
    print(f"🚀 PHÂN TÍCH CHO RUST TEMPLATING ENGINE")
    print(f"{'='*60}")
    
    print(f"\n📋 Khả năng sử dụng làm dữ liệu template:")
    
    # Phân tích CSV
    if csv_df is not None:
        print(f"\n📄 File CSV (dulieu.csv):")
        print(f"   - Có thể dùng làm data source: ✅")
        print(f"   - Số record: {len(csv_df)}")
        print(f"   - Template placeholders có thể tạo:")
        for col in csv_df.columns:
            print(f"     {{{{ {col} }}}}")
        
        # Tạo ví dụ JSON data
        print(f"\n   - Ví dụ JSON data cho template:")
        sample_record = csv_df.iloc[0].to_dict() if len(csv_df) > 0 else {}
        print(f"     {sample_record}")
    
    # Phân tích XLSX
    if xlsx_data is not None:
        print(f"\n📄 File XLSX (MNF (1).xlsx):")
        for sheet_name, df in xlsx_data.items():
            print(f"\n   Sheet '{sheet_name}':")
            print(f"   - Có thể dùng làm data source: ✅")
            print(f"   - Số record: {len(df)}")
            print(f"   - Template placeholders có thể tạo:")
            for col in df.columns:
                print(f"     {{{{ {col} }}}}")
    
    print(f"\n🔧 Cách tích hợp với Rust templating engine:")
    print(f"   1. Convert CSV/XLSX thành JSON data")
    print(f"   2. Sử dụng JsonDataSource trong engine")
    print(f"   3. Tạo template với placeholders tương ứng")
    print(f"   4. Render template với dữ liệu")

def main():
    """Hàm chính"""
    print("🔍 CHƯƠNG TRÌNH PHÂN TÍCH DỮ LIỆU CSV VÀ XLSX")
    print("=" * 60)
    
    # Đường dẫn file
    csv_file = "D:/InsertData/dulieu.csv"
    xlsx_file = "D:/InsertData/MNF (1).xlsx"
    
    # Kiểm tra file tồn tại
    csv_exists = os.path.exists(csv_file)
    xlsx_exists = os.path.exists(xlsx_file)
    
    print(f"📁 Kiểm tra file:")
    print(f"   - dulieu.csv: {'✅ Tồn tại' if csv_exists else '❌ Không tồn tại'}")
    print(f"   - MNF (1).xlsx: {'✅ Tồn tại' if xlsx_exists else '❌ Không tồn tại'}")
    
    # Phân tích CSV
    csv_df = None
    if csv_exists:
        csv_df = analyze_csv_file(csv_file)
    
    # Phân tích XLSX
    xlsx_data = None
    if xlsx_exists:
        xlsx_data = analyze_xlsx_file(xlsx_file)
    
    # Phân tích cho templating engine
    if csv_df is not None or xlsx_data is not None:
        analyze_for_templating_engine(csv_df, xlsx_data)
    
    print(f"\n✅ Hoàn thành phân tích!")

if __name__ == "__main__":
    main()
