D:\InsertData\rust-templating-engine\target\debug\deps\librust_templating_engine-c8be9cdce4433933.rmeta: src\lib.rs src\config\mod.rs src\config\builder.rs src\config\defaults.rs src\document\mod.rs src\engine.rs src\error\mod.rs src\error\types.rs src\error\context.rs src\error\conversion.rs src\modules\mod.rs src\render\mod.rs src\template\mod.rs src\utils\mod.rs src\utils\memory.rs src\utils\buffer_pool.rs src\utils\pressure.rs src\utils\backpressure.rs

D:\InsertData\rust-templating-engine\target\debug\deps\librust_templating_engine-c8be9cdce4433933.rlib: src\lib.rs src\config\mod.rs src\config\builder.rs src\config\defaults.rs src\document\mod.rs src\engine.rs src\error\mod.rs src\error\types.rs src\error\context.rs src\error\conversion.rs src\modules\mod.rs src\render\mod.rs src\template\mod.rs src\utils\mod.rs src\utils\memory.rs src\utils\buffer_pool.rs src\utils\pressure.rs src\utils\backpressure.rs

D:\InsertData\rust-templating-engine\target\debug\deps\rust_templating_engine-c8be9cdce4433933.d: src\lib.rs src\config\mod.rs src\config\builder.rs src\config\defaults.rs src\document\mod.rs src\engine.rs src\error\mod.rs src\error\types.rs src\error\context.rs src\error\conversion.rs src\modules\mod.rs src\render\mod.rs src\template\mod.rs src\utils\mod.rs src\utils\memory.rs src\utils\buffer_pool.rs src\utils\pressure.rs src\utils\backpressure.rs

src\lib.rs:
src\config\mod.rs:
src\config\builder.rs:
src\config\defaults.rs:
src\document\mod.rs:
src\engine.rs:
src\error\mod.rs:
src\error\types.rs:
src\error\context.rs:
src\error\conversion.rs:
src\modules\mod.rs:
src\render\mod.rs:
src\template\mod.rs:
src\utils\mod.rs:
src\utils\memory.rs:
src\utils\buffer_pool.rs:
src\utils\pressure.rs:
src\utils\backpressure.rs:

# env-dep:CARGO_PKG_DESCRIPTION=A high-performance templating engine for Office documents (docx, pptx, xlsx) written in Rust
# env-dep:CARGO_PKG_NAME=rust-templating-engine
# env-dep:CARGO_PKG_VERSION=0.1.0
