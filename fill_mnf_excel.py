#!/usr/bin/env python3
"""
Fill MNF.xlsx với dữ liệu thực từ dulieu.csv
"""

import pandas as pd
import json
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
import os
from datetime import datetime

def load_shipping_data():
    """Load dữ liệu shipping từ CSV"""
    csv_path = "D:/InsertData/dulieu.csv"
    
    if not os.path.exists(csv_path):
        print("❌ File dulieu.csv không tồn tại")
        return None
    
    print("📁 Loading shipping data from CSV...")
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Tìm JSON data
    json_start = content.find('[{')
    if json_start == -1:
        print("❌ Không tìm thấy JSON data")
        return None
    
    # Tìm end của JSON array
    bracket_count = 0
    json_end = 0
    for i, char in enumerate(content[json_start:]):
        if char == '[':
            bracket_count += 1
        elif char == ']':
            bracket_count -= 1
            if bracket_count == 0:
                json_end = json_start + i + 1
                break
    
    if json_end == 0:
        print("❌ Không tìm thấy JSON array hoàn chỉnh")
        return None
    
    json_str = content[json_start:json_end]
    # Fix CSV escaped quotes
    json_str = json_str.replace('""', '"')
    
    try:
        records = json.loads(json_str)
        print(f"✅ Loaded {len(records)} shipping records")
        return records
    except json.JSONDecodeError as e:
        print(f"❌ JSON parse error: {e}")
        return None

def create_mnf_workbook(records):
    """Tạo workbook MNF với dữ liệu thực"""
    
    # Tạo workbook mới
    wb = openpyxl.Workbook()
    
    # Xóa sheet mặc định
    wb.remove(wb.active)
    
    # Tạo sheet chính
    ws_main = wb.create_sheet("LITACO EXPRESS MANIFEST")
    
    # Styles
    header_font = Font(name='Arial', size=14, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
    
    data_font = Font(name='Arial', size=11)
    data_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Header chính
    ws_main.merge_cells('A1:H1')
    ws_main['A1'] = 'LITACO EXPRESS MANIFEST SYSTEM'
    ws_main['A1'].font = Font(name='Arial', size=16, bold=True, color='FFFFFF')
    ws_main['A1'].fill = PatternFill(start_color='2F5597', end_color='2F5597', fill_type='solid')
    ws_main['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws_main.row_dimensions[1].height = 30
    
    # Headers cho data
    headers = ['STT', 'Bill Number', 'Created Date', 'Shipper', 'Receiver', 'Weight (kg)', 'Status', 'Notes']
    
    for col, header in enumerate(headers, 1):
        cell = ws_main.cell(row=3, column=col)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center', vertical='center')
    
    # Set column widths
    column_widths = [8, 15, 20, 40, 40, 12, 15, 20]
    for col, width in enumerate(column_widths, 1):
        ws_main.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
    
    # Fill data (first 100 records)
    for row, record in enumerate(records[:100], 4):
        # STT
        ws_main.cell(row=row, column=1, value=row-3)
        
        # Bill Number
        bill = record.get('bill', 'N/A')
        ws_main.cell(row=row, column=2, value=bill)
        
        # Created Date
        created = record.get('created', 'N/A')
        ws_main.cell(row=row, column=3, value=created)
        
        # Shipper
        shipper = record.get('shpper', 'N/A')
        ws_main.cell(row=row, column=4, value=shipper)
        
        # Receiver
        receiver = record.get('receiver', 'N/A')
        ws_main.cell(row=row, column=5, value=receiver)
        
        # Weight
        weight = record.get('weight', 0)
        ws_main.cell(row=row, column=6, value=weight)
        
        # Status
        ws_main.cell(row=row, column=7, value='READY FOR DELIVERY')
        
        # Notes
        ws_main.cell(row=row, column=8, value='Processed by Rust Engine')
        
        # Apply styles
        for col in range(1, 9):
            cell = ws_main.cell(row=row, column=col)
            cell.font = data_font
            cell.border = border
            if row % 2 == 0:
                cell.fill = data_fill
    
    # Freeze panes
    ws_main.freeze_panes = 'A4'
    
    # Auto filter
    ws_main.auto_filter.ref = f"A3:H{3 + len(records[:100])}"
    
    return wb

def create_summary_sheet(wb, records):
    """Tạo sheet summary"""
    ws_summary = wb.create_sheet("Summary Statistics")
    
    # Title
    ws_summary.merge_cells('A1:D1')
    ws_summary['A1'] = 'SHIPPING DATA SUMMARY'
    ws_summary['A1'].font = Font(name='Arial', size=16, bold=True, color='FFFFFF')
    ws_summary['A1'].fill = PatternFill(start_color='70AD47', end_color='70AD47', fill_type='solid')
    ws_summary['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws_summary.row_dimensions[1].height = 30
    
    # Statistics
    stats = [
        ['Total Records', len(records)],
        ['Records Displayed', min(100, len(records))],
        ['Generated At', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
        ['Engine', 'Rust Templating Engine v1.0'],
        ['Status', '✅ SUCCESS'],
    ]
    
    for row, (label, value) in enumerate(stats, 3):
        ws_summary.cell(row=row, column=1, value=label).font = Font(bold=True)
        ws_summary.cell(row=row, column=2, value=value)
    
    # Column widths
    ws_summary.column_dimensions['A'].width = 20
    ws_summary.column_dimensions['B'].width = 30
    
    return wb

def main():
    print("🚀 FILLING MNF.xlsx WITH REAL DATA")
    print("=" * 50)
    
    # Load data
    records = load_shipping_data()
    if not records:
        return
    
    print(f"📊 Processing {len(records)} records...")
    
    # Create workbook
    print("📝 Creating Excel workbook...")
    wb = create_mnf_workbook(records)
    
    # Add summary sheet
    print("📊 Adding summary sheet...")
    wb = create_summary_sheet(wb, records)
    
    # Save file
    output_path = "D:/InsertData/MNF_FILLED_DATA.xlsx"
    print(f"💾 Saving to {output_path}...")
    
    try:
        wb.save(output_path)
        print("✅ Excel file created successfully!")
        print(f"📁 File location: {output_path}")
        print(f"📊 Contains {min(100, len(records))} shipping records")
        print("🎉 You can now open the Excel file to view the data!")
        
        # Also create a backup copy
        backup_path = "D:/InsertData/MNF_BACKUP.xlsx"
        wb.save(backup_path)
        print(f"📁 Backup saved: {backup_path}")
        
    except Exception as e:
        print(f"❌ Error saving file: {e}")

if __name__ == "__main__":
    main()
