[package]
name = "rust-templating-engine"
version = "0.1.0"
edition = "2021"
authors = ["Rust Templating Engine Team"]
description = "A high-performance templating engine for Office documents (docx, pptx, xlsx) written in Rust"
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-templating-engine/rust-templating-engine"
keywords = ["template", "docx", "xlsx", "pptx", "office"]
categories = ["template-engine", "text-processing"]

[dependencies]
# Error handling
thiserror = "1.0"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Async runtime (for pressure detection)
tokio = { version = "1.35", features = ["time", "sync", "rt-multi-thread", "macros"] }

# CSV processing (simpler than Excel)
csv = "1.3"

[dev-dependencies]
# Testing
tempfile = "3.8"
tokio-test = "0.4"



[features]
default = []
compression = []
async = []
memory-profiling = []

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.bench]
opt-level = 3
lto = true
codegen-units = 1
