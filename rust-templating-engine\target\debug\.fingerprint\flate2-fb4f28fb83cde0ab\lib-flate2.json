{"rustc": 12200708597866198150, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 1010991399650310101, "deps": [[7312356825837975969, "crc32fast", false, 17372702450592925645], [7636735136738807108, "miniz_oxide", false, 14603517413148975951]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-fb4f28fb83cde0ab\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}