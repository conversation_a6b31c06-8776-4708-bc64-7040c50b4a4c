//! # Rust Templating Engine
//!
//! A high-performance templating engine for Office documents (docx, pptx, xlsx) written in Rust.
//! 
//! This library provides a memory-efficient, type-safe alternative to docxtemplater with
//! significant performance improvements through streaming processing, regex optimization,
//! and async I/O.
//!
//! ## Features
//!
//! - **Memory Efficient**: Streaming processing with configurable memory limits
//! - **Type Safe**: Compile-time guarantees with Rust's type system
//! - **High Performance**: Regex caching, async I/O, parallel processing
//! - **Extensible**: Trait-based module system
//! - **Compatible**: Support for docxtemplater template syntax
//!
//! ## Quick Start
//!
//! ```rust,no_run
//! use rust_templating_engine::{TemplateEngine, EngineConfig};
//! use serde_json::json;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     let config = EngineConfig::builder()
//!         .memory_limit(64 * 1024 * 1024) // 64MB
//!         .delimiters("{", "}")
//!         .build();
//!     
//!     let engine = TemplateEngine::new(config);
//!     
//!     let data = json!({
//!         "name": "John Doe",
//!         "items": [
//!             {"name": "Item 1", "price": 100},
//!             {"name": "Item 2", "price": 200}
//!         ]
//!     });
//!     
//!     engine.process_template(
//!         "template.docx",
//!         "output.docx", 
//!         &data
//!     ).await?;
//!     
//!     Ok(())
//! }
//! ```

// Re-exports for public API
pub use crate::engine::TemplateEngine;
pub use crate::config::{EngineConfig, EngineConfigBuilder};
pub use crate::error::{Result, TemplateError};

// Core modules
pub mod config;
pub mod document;
pub mod engine;
pub mod error;
pub mod modules;
pub mod render;
pub mod template;
pub mod utils;
pub mod excel;

// Internal modules (not part of public API)
mod internal {
    //! Internal utilities and helpers not exposed in public API
}

/// Template engine version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Template engine name
pub const NAME: &str = env!("CARGO_PKG_NAME");

/// Template engine description
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

/// Prelude module for convenient imports
pub mod prelude {
    //! Convenient re-exports for common use cases
    
    pub use crate::config::{EngineConfig, EngineConfigBuilder};
    pub use crate::engine::TemplateEngine;
    pub use crate::error::{Result, TemplateError};
    pub use crate::modules::{Module, ModuleManager};
    pub use crate::render::{DataSource, RenderEngine};
    pub use crate::template::{Template, TemplateProcessor};
}

/// Common type aliases for convenience
pub mod types {
    //! Common type definitions used throughout the library
    
    use std::collections::HashMap;
    use serde_json::Value;
    
    /// JSON data type for template rendering
    pub type JsonData = Value;
    
    /// Template variables map
    pub type TemplateVars = HashMap<String, Value>;
    
    /// File path type
    pub type FilePath = std::path::PathBuf;
    
    /// Memory size in bytes
    pub type MemorySize = usize;
    
    /// Processing batch size
    pub type BatchSize = usize;
}

/// Feature flags and capabilities
pub mod features {
    //! Feature detection and capability flags
    
    /// Check if compression feature is enabled
    pub const fn has_compression() -> bool {
        cfg!(feature = "compression")
    }
    
    /// Check if async feature is enabled
    pub const fn has_async() -> bool {
        cfg!(feature = "async")
    }
    
    /// Check if memory profiling is enabled
    pub const fn has_memory_profiling() -> bool {
        cfg!(feature = "memory-profiling")
    }
}

/// Utility functions for common operations
pub mod utils_public {
    //! Public utility functions
    
    use crate::error::Result;
    use std::path::Path;
    
    /// Validate template file format
    pub fn validate_template_format<P: AsRef<Path>>(path: P) -> Result<()> {
        let path = path.as_ref();
        let extension = path.extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| crate::error::TemplateError::InvalidFormat {
                message: "Missing file extension".to_string(),
            })?;
        
        match extension.to_lowercase().as_str() {
            "docx" | "pptx" | "xlsx" => Ok(()),
            _ => Err(crate::error::TemplateError::InvalidFormat {
                message: format!("Unsupported file format: {}", extension),
            }),
        }
    }
    
    /// Get memory usage statistics
    pub fn get_memory_stats() -> crate::utils::memory::MemoryStats {
        // This will be implemented in Task 3
        crate::utils::memory::MemoryStats::default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_version_info() {
        assert!(!VERSION.is_empty());
        assert!(!NAME.is_empty());
        assert!(!DESCRIPTION.is_empty());
    }
    
    #[test]
    fn test_feature_flags() {
        // Test feature detection
        let _has_compression = features::has_compression();
        let _has_async = features::has_async();
        let _has_profiling = features::has_memory_profiling();
    }
    
    #[test]
    fn test_template_format_validation() {
        use utils_public::validate_template_format;
        
        assert!(validate_template_format("test.docx").is_ok());
        assert!(validate_template_format("test.pptx").is_ok());
        assert!(validate_template_format("test.xlsx").is_ok());
        assert!(validate_template_format("test.txt").is_err());
    }
}
