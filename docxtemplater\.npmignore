*.data
*.es5
*.log
*.old
.edb-*
stryker.conf.js
.prettierignore
.eslintcache
.editorconfig
babel.config.js
.prettierrc.yaml
webdriver.mjs
__snapshots.js
.github/
eslint.config.mjs
.eslint-rules/
*.pdf
*.tgz
*.tar.gz
*.png
*.sublime-project
*.sublime-workspace
.babelrc
.~lock*
.env
.nyc_output
.idea
/*.docx
/*.pptx
browser
cli
es6
ISSUE_TEMPLATE.md
vendor
*.yml
*.bash
/docs
/docs/build
/examples
/output.docx
*.spec.js
/test
coverage
examples/index.html
node_modules
p.json
prof
profile_output_total.json
tags
browser-demo
/js/tests/*.xml
.stryker-tmp
reports
