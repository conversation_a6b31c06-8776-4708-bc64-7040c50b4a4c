{"rustc": 12200708597866198150, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 688040178356337507, "deps": [[555019317135488525, "regex_automata", false, 7828138436869330652], [2779309023524819297, "aho_corasick", false, 13728524634943716432], [9408802513701742484, "regex_syntax", false, 7859584968247050052], [15932120279885307830, "memchr", false, 12578421377346765309]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-5940dcf8175eaafa\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}