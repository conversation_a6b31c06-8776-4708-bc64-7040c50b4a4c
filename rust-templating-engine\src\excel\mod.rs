//! CSV export functionality

use crate::error::Result;
use serde_json::Value;
use csv::Writer;
use std::path::Path;
use std::fs::File;

/// CSV exporter for shipping data
pub struct CsvExporter {
    output_dir: String,
}

impl CsvExporter {
    /// Create new CSV exporter
    pub fn new<P: AsRef<Path>>(output_dir: P) -> Result<Self> {
        let output_dir = output_dir.as_ref().to_string_lossy().to_string();
        std::fs::create_dir_all(&output_dir)?;
        Ok(Self { output_dir })
    }
    
    /// Export shipping data to CSV
    pub fn export_shipping_data(&self, records: &[Value]) -> Result<String> {
        if records.is_empty() {
            return Ok("No data to export".to_string());
        }
        
        // Auto-detect all fields from data
        let mut all_fields = std::collections::BTreeSet::new();
        for record in records {
            if let Some(obj) = record.as_object() {
                for key in obj.keys() {
                    all_fields.insert(key.clone());
                }
            }
        }
        
        let field_list: Vec<String> = all_fields.into_iter().collect();
        println!("📊 Auto-detected fields: {:?}", field_list);
        
        // Create CSV file
        let csv_path = format!("{}/shipping_data.csv", self.output_dir);
        let file = File::create(&csv_path)?;
        let mut writer = Writer::from_writer(file);
        
        // Write headers
        let mut headers = vec!["STT".to_string()];
        headers.extend(field_list.clone());
        writer.write_record(&headers)?;
        
        // Write data
        for (row, record) in records.iter().enumerate() {
            let mut csv_record = vec![(row + 1).to_string()];
            
            if let Some(obj) = record.as_object() {
                for field in &field_list {
                    let value = if let Some(v) = obj.get(field) {
                        match v {
                            Value::String(s) => s.clone(),
                            Value::Number(n) => n.to_string(),
                            Value::Bool(b) => b.to_string(),
                            _ => v.to_string().trim_matches('"').to_string(),
                        }
                    } else {
                        "".to_string()
                    };
                    csv_record.push(value);
                }
            }
            
            writer.write_record(&csv_record)?;
        }
        
        writer.flush()?;
        Ok(csv_path)
    }
    
    /// Export manifest data to CSV
    pub fn export_manifest_data(&self, manifests: &[(Value, String)]) -> Result<String> {
        let csv_path = format!("{}/manifests.csv", self.output_dir);
        let file = File::create(&csv_path)?;
        let mut writer = Writer::from_writer(file);
        
        // Write headers
        writer.write_record(&["STT", "Bill Number", "Receiver", "Weight", "Generated Manifest"])?;
        
        // Write data
        for (row, (record, manifest)) in manifests.iter().enumerate() {
            let bill = record.get("bill").and_then(|v| v.as_str()).unwrap_or("N/A");
            let receiver = record.get("receiver").and_then(|v| v.as_str()).unwrap_or("N/A");
            let weight = record.get("weight").and_then(|v| v.as_f64()).unwrap_or(0.0);
            
            writer.write_record(&[
                &(row + 1).to_string(),
                bill,
                receiver,
                &weight.to_string(),
                manifest,
            ])?;
        }
        
        writer.flush()?;
        Ok(csv_path)
    }
    
    /// Export summary statistics
    pub fn export_summary(&self, total_records: usize, processing_time: std::time::Duration, throughput: f64) -> Result<String> {
        let csv_path = format!("{}/summary.csv", self.output_dir);
        let file = File::create(&csv_path)?;
        let mut writer = Writer::from_writer(file);
        
        // Write headers
        writer.write_record(&["Metric", "Value"])?;
        
        // Write statistics
        writer.write_record(&["Total Records Processed", &total_records.to_string()])?;
        writer.write_record(&["Processing Time", &format!("{:.2?}", processing_time)])?;
        writer.write_record(&["Throughput", &format!("{:.2} records/sec", throughput)])?;
        writer.write_record(&["Engine Status", "✅ SUCCESS"])?;
        writer.write_record(&["Generated At", &format!("{:?}", std::time::SystemTime::now())])?;
        
        writer.flush()?;
        Ok(csv_path)
    }
}