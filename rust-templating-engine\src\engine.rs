//! Main template engine implementation

use crate::config::EngineConfig;
use crate::error::Result;
use std::path::Path;

/// Main template engine
pub struct TemplateEngine {
    config: EngineConfig,
}

impl TemplateEngine {
    /// Create a new template engine with the given configuration
    pub fn new(config: EngineConfig) -> Self {
        Self { config }
    }
    
    /// Create a template engine with default configuration
    pub fn default() -> Self {
        Self::new(EngineConfig::default())
    }
    
    /// Process a template file with data and save to output
    pub async fn process_template<P1, P2, D>(
        &self,
        input_path: P1,
        output_path: P2,
        data: &D,
    ) -> Result<()>
    where
        P1: AsRef<Path>,
        P2: AsRef<Path>,
        D: serde::Serialize,
    {
        use crate::document::DocumentLoader;
        use crate::template::TemplateProcessor;
        use crate::render::{RenderEngine, JsonDataSource};
        // use crate::modules::ModuleManager;

        let input = input_path.as_ref();
        let output = output_path.as_ref();

        // Validate input file format
        crate::utils_public::validate_template_format(input)?;

        // 1. Load document
        let loader = DocumentLoader::new();
        let document = loader.load(input).await?;

        // 2. Parse templates
        let _processor = TemplateProcessor::new(self.config.clone());
        let template = crate::template::Template::from_content(
            String::from_utf8_lossy(&document.files.get("document.xml").unwrap_or(&Vec::new())).to_string()
        );

        // 3. Convert data to JSON
        let json_data = serde_json::to_value(data)
            .map_err(|e| crate::error::TemplateError::Internal {
                message: format!("Failed to serialize data: {}", e),
            })?;

        // 4. Render with data
        let data_source = JsonDataSource::new(json_data);
        let render_engine = RenderEngine::new(self.config.clone());
        let rendered = render_engine.render(&template, &data_source).await?;

        // 5. Save output (simplified)
        std::fs::write(output, rendered)
            .map_err(|e| crate::error::TemplateError::Io(e))?;

        Ok(())
    }

    /// Process template content directly
    pub async fn process_content<D>(&self, content: &str, data: &D) -> Result<String>
    where
        D: serde::Serialize,
    {
        use crate::template::Template;
        use crate::render::{RenderEngine, JsonDataSource};

        // Create template from content
        let template = Template::from_content(content.to_string());

        // Convert data to JSON
        let json_data = serde_json::to_value(data)
            .map_err(|e| crate::error::TemplateError::Internal {
                message: format!("Failed to serialize data: {}", e),
            })?;

        // Render
        let data_source = JsonDataSource::new(json_data);
        let render_engine = RenderEngine::new(self.config.clone());
        render_engine.render(&template, &data_source).await
    }
    
    /// Get engine configuration
    pub fn config(&self) -> &EngineConfig {
        &self.config
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;
    
    #[test]
    fn test_engine_creation() {
        let engine = TemplateEngine::default();
        assert_eq!(engine.config().memory_limit, 64 * 1024 * 1024);
    }
    
    #[tokio::test]
    async fn test_template_validation() {
        let engine = TemplateEngine::default();
        let data = json!({"name": "test"});
        
        // Valid format should not error immediately
        let result = engine.process_template("test.docx", "output.docx", &data).await;
        // Will fail because file doesn't exist, but format validation should pass
        assert!(result.is_ok() || matches!(result, Err(crate::error::TemplateError::Io(_))));
        
        // Invalid format should error
        let result = engine.process_template("test.txt", "output.txt", &data).await;
        assert!(result.is_err());
    }
}
