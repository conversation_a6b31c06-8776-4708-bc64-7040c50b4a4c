//! Default configuration values and constants

use super::*;

/// Default configuration constants
pub mod defaults {
    use super::*;
    
    // Memory defaults
    pub const DEFAULT_MEMORY_LIMIT: usize = 64 * 1024 * 1024; // 64MB
    pub const MIN_MEMORY_LIMIT: usize = 1024 * 1024; // 1MB
    pub const MAX_MEMORY_LIMIT: usize = 2 * 1024 * 1024 * 1024; // 2GB
    
    // Batch processing defaults
    pub const DEFAULT_BATCH_SIZE: usize = 1000;
    pub const MIN_BATCH_SIZE: usize = 1;
    pub const MAX_BATCH_SIZE: usize = 100_000;
    
    // Delimiter defaults
    pub const DEFAULT_START_DELIMITER: &str = "{";
    pub const DEFAULT_END_DELIMITER: &str = "}";
    pub const DEFAULT_CHANGE_PREFIX: &str = "=";
    pub const MAX_DELIMITER_LENGTH: usize = 10;
    
    // Syntax defaults
    pub const DEFAULT_LOOP_START_PREFIX: &str = "#";
    pub const DEFAULT_LOOP_END_PREFIX: &str = "/";
    pub const DEFAULT_CONDITION_PREFIX: &str = "?";
    pub const DEFAULT_RAW_XML_PREFIX: &str = "@";
    pub const DEFAULT_CASE_SENSITIVE: bool = false;
    
    // Error handling defaults
    pub const DEFAULT_FAIL_FAST: bool = false;
    pub const DEFAULT_COLLECT_ALL_ERRORS: bool = true;
    pub const DEFAULT_MAX_ERRORS: usize = 100;
    pub const DEFAULT_INCLUDE_CONTEXT: bool = true;
    pub const MAX_ERROR_LIMIT: usize = 10_000;
    
    // Performance defaults
    pub const DEFAULT_REGEX_CACHE_SIZE: usize = 100;
    pub const DEFAULT_BUFFER_POOL_SIZE: usize = 50;
    pub const DEFAULT_IO_TIMEOUT_SECS: u64 = 30;
    pub const DEFAULT_BACKPRESSURE_THRESHOLD: f64 = 0.8;
    pub const MAX_CONCURRENT_TASKS: usize = 1000;
    pub const MIN_BACKPRESSURE_THRESHOLD: f64 = 0.1;
    pub const MAX_BACKPRESSURE_THRESHOLD: f64 = 1.0;
}

/// Environment-specific defaults
pub mod env_defaults {
    use super::defaults::*;
    use std::time::Duration;
    
    /// Get default configuration for development environment
    pub fn development() -> super::EngineConfig {
        super::EngineConfig {
            memory_limit: 32 * 1024 * 1024, // 32MB for dev
            batch_size: 500,
            delimiters: super::DelimiterConfig {
                start: DEFAULT_START_DELIMITER.to_string(),
                end: DEFAULT_END_DELIMITER.to_string(),
                change_prefix: Some(DEFAULT_CHANGE_PREFIX.to_string()),
            },
            syntax: super::SyntaxConfig {
                loop_start_prefix: DEFAULT_LOOP_START_PREFIX.to_string(),
                loop_end_prefix: DEFAULT_LOOP_END_PREFIX.to_string(),
                condition_prefix: DEFAULT_CONDITION_PREFIX.to_string(),
                raw_xml_prefix: DEFAULT_RAW_XML_PREFIX.to_string(),
                case_sensitive: DEFAULT_CASE_SENSITIVE,
            },
            error_handling: super::ErrorHandlingConfig {
                fail_fast: true, // Fail fast in development
                collect_all_errors: true,
                max_errors: 50,
                include_context: true,
            },
            performance: super::PerformanceConfig {
                regex_cache_size: 50,
                buffer_pool_size: 25,
                max_concurrent_tasks: 2,
                io_timeout: Duration::from_secs(10),
                backpressure_threshold: 0.7,
            },
        }
    }
    
    /// Get default configuration for testing environment
    pub fn testing() -> super::EngineConfig {
        super::EngineConfig {
            memory_limit: 16 * 1024 * 1024, // 16MB for tests
            batch_size: 100,
            delimiters: super::DelimiterConfig {
                start: DEFAULT_START_DELIMITER.to_string(),
                end: DEFAULT_END_DELIMITER.to_string(),
                change_prefix: Some(DEFAULT_CHANGE_PREFIX.to_string()),
            },
            syntax: super::SyntaxConfig {
                loop_start_prefix: DEFAULT_LOOP_START_PREFIX.to_string(),
                loop_end_prefix: DEFAULT_LOOP_END_PREFIX.to_string(),
                condition_prefix: DEFAULT_CONDITION_PREFIX.to_string(),
                raw_xml_prefix: DEFAULT_RAW_XML_PREFIX.to_string(),
                case_sensitive: DEFAULT_CASE_SENSITIVE,
            },
            error_handling: super::ErrorHandlingConfig {
                fail_fast: true,
                collect_all_errors: true,
                max_errors: 20,
                include_context: true,
            },
            performance: super::PerformanceConfig {
                regex_cache_size: 20,
                buffer_pool_size: 10,
                max_concurrent_tasks: 1,
                io_timeout: Duration::from_secs(5),
                backpressure_threshold: 0.6,
            },
        }
    }
    
    /// Get default configuration for production environment
    pub fn production() -> super::EngineConfig {
        super::EngineConfig {
            memory_limit: DEFAULT_MEMORY_LIMIT,
            batch_size: DEFAULT_BATCH_SIZE,
            delimiters: super::DelimiterConfig {
                start: DEFAULT_START_DELIMITER.to_string(),
                end: DEFAULT_END_DELIMITER.to_string(),
                change_prefix: Some(DEFAULT_CHANGE_PREFIX.to_string()),
            },
            syntax: super::SyntaxConfig {
                loop_start_prefix: DEFAULT_LOOP_START_PREFIX.to_string(),
                loop_end_prefix: DEFAULT_LOOP_END_PREFIX.to_string(),
                condition_prefix: DEFAULT_CONDITION_PREFIX.to_string(),
                raw_xml_prefix: DEFAULT_RAW_XML_PREFIX.to_string(),
                case_sensitive: DEFAULT_CASE_SENSITIVE,
            },
            error_handling: super::ErrorHandlingConfig {
                fail_fast: DEFAULT_FAIL_FAST,
                collect_all_errors: false, // Don't collect all in production
                max_errors: 10,
                include_context: false, // Less context in production
            },
            performance: super::PerformanceConfig {
                regex_cache_size: DEFAULT_REGEX_CACHE_SIZE,
                buffer_pool_size: DEFAULT_BUFFER_POOL_SIZE,
                max_concurrent_tasks: super::num_cpus(),
                io_timeout: Duration::from_secs(DEFAULT_IO_TIMEOUT_SECS),
                backpressure_threshold: DEFAULT_BACKPRESSURE_THRESHOLD,
            },
        }
    }
}

/// Configuration validation utilities
pub mod validation {
    use super::defaults::*;
    use crate::error::{Result, TemplateError};
    
    /// Validate memory limit
    pub fn validate_memory_limit(limit: usize) -> Result<()> {
        if limit == 0 {
            return Err(TemplateError::Configuration {
                message: "Memory limit cannot be zero".to_string(),
            });
        }
        
        if limit < MIN_MEMORY_LIMIT {
            return Err(TemplateError::Configuration {
                message: format!("Memory limit too small (min {})", MIN_MEMORY_LIMIT),
            });
        }
        
        if limit > MAX_MEMORY_LIMIT {
            return Err(TemplateError::Configuration {
                message: format!("Memory limit too large (max {})", MAX_MEMORY_LIMIT),
            });
        }
        
        Ok(())
    }
    
    /// Validate batch size
    pub fn validate_batch_size(size: usize) -> Result<()> {
        if size == 0 {
            return Err(TemplateError::Configuration {
                message: "Batch size cannot be zero".to_string(),
            });
        }
        
        if size > MAX_BATCH_SIZE {
            return Err(TemplateError::Configuration {
                message: format!("Batch size too large (max {})", MAX_BATCH_SIZE),
            });
        }
        
        Ok(())
    }
    
    /// Validate delimiters
    pub fn validate_delimiters(start: &str, end: &str) -> Result<()> {
        if start.is_empty() || end.is_empty() {
            return Err(TemplateError::Configuration {
                message: "Delimiters cannot be empty".to_string(),
            });
        }
        
        if start == end {
            return Err(TemplateError::Configuration {
                message: "Start and end delimiters must be different".to_string(),
            });
        }
        
        if start.len() > MAX_DELIMITER_LENGTH || end.len() > MAX_DELIMITER_LENGTH {
            return Err(TemplateError::Configuration {
                message: format!("Delimiters too long (max {} characters)", MAX_DELIMITER_LENGTH),
            });
        }
        
        // Check for problematic characters
        for ch in start.chars().chain(end.chars()) {
            if ch.is_control() && ch != '\t' {
                return Err(TemplateError::Configuration {
                    message: "Delimiters cannot contain control characters".to_string(),
                });
            }
        }
        
        Ok(())
    }
    
    /// Validate error handling configuration
    pub fn validate_error_handling(config: &super::ErrorHandlingConfig) -> Result<()> {
        if config.max_errors == 0 {
            return Err(TemplateError::Configuration {
                message: "Max errors cannot be zero".to_string(),
            });
        }
        
        if config.max_errors > MAX_ERROR_LIMIT {
            return Err(TemplateError::Configuration {
                message: format!("Max errors too large (max {})", MAX_ERROR_LIMIT),
            });
        }
        
        Ok(())
    }
    
    /// Validate performance configuration
    pub fn validate_performance(config: &super::PerformanceConfig) -> Result<()> {
        if config.regex_cache_size == 0 {
            return Err(TemplateError::Configuration {
                message: "Regex cache size cannot be zero".to_string(),
            });
        }
        
        if config.buffer_pool_size == 0 {
            return Err(TemplateError::Configuration {
                message: "Buffer pool size cannot be zero".to_string(),
            });
        }
        
        if config.max_concurrent_tasks == 0 {
            return Err(TemplateError::Configuration {
                message: "Max concurrent tasks cannot be zero".to_string(),
            });
        }
        
        if config.max_concurrent_tasks > MAX_CONCURRENT_TASKS {
            return Err(TemplateError::Configuration {
                message: format!("Too many concurrent tasks (max {})", MAX_CONCURRENT_TASKS),
            });
        }
        
        if config.backpressure_threshold < MIN_BACKPRESSURE_THRESHOLD 
            || config.backpressure_threshold > MAX_BACKPRESSURE_THRESHOLD {
            return Err(TemplateError::Configuration {
                message: format!(
                    "Backpressure threshold must be between {} and {}",
                    MIN_BACKPRESSURE_THRESHOLD,
                    MAX_BACKPRESSURE_THRESHOLD
                ),
            });
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_env_defaults() {
        let dev_config = env_defaults::development();
        assert_eq!(dev_config.memory_limit, 32 * 1024 * 1024);
        assert!(dev_config.error_handling.fail_fast);
        
        let test_config = env_defaults::testing();
        assert_eq!(test_config.memory_limit, 16 * 1024 * 1024);
        assert_eq!(test_config.batch_size, 100);
        
        let prod_config = env_defaults::production();
        assert_eq!(prod_config.memory_limit, defaults::DEFAULT_MEMORY_LIMIT);
        assert!(!prod_config.error_handling.collect_all_errors);
    }
    
    #[test]
    fn test_validation() {
        use validation::*;
        
        // Valid cases
        assert!(validate_memory_limit(64 * 1024 * 1024).is_ok());
        assert!(validate_batch_size(1000).is_ok());
        assert!(validate_delimiters("{", "}").is_ok());
        
        // Invalid cases
        assert!(validate_memory_limit(0).is_err());
        assert!(validate_batch_size(0).is_err());
        assert!(validate_delimiters("", "}").is_err());
        assert!(validate_delimiters("{", "{").is_err());
    }
}
