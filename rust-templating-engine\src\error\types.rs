//! Error type definitions using thiserror

/// Main error type for the templating engine
#[derive(Debug, thiserror::Error)]
pub enum TemplateError {
    #[error("Document loading failed: {source}")]
    DocumentLoad {
        #[from]
        source: DocumentLoadError,
    },
    
    #[error("Template parsing failed: {source}")]
    TemplateParse {
        #[from]
        source: TemplateParseError,
    },
    
    #[error("Rendering failed: {source}")]
    Render {
        #[from]
        source: RenderError,
    },
    
    #[error("Memory limit exceeded: {current} bytes > {limit} bytes")]
    MemoryLimit {
        current: usize,
        limit: usize,
    },
    
    #[error("Invalid template format: {message}")]
    InvalidFormat {
        message: String,
    },
    
    #[error("Configuration error: {message}")]
    Configuration {
        message: String,
    },
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Internal error: {message}")]
    Internal {
        message: String,
    },
}

/// Document loading errors
#[derive(Debug, thiserror::Error)]
pub enum DocumentLoadError {
    #[error("Invalid ZIP file: {message}")]
    InvalidZip {
        message: String,
    },
    
    #[error("Missing required file: {file}")]
    MissingFile {
        file: String,
    },
    
    #[error("Unsupported file type: {file_type}")]
    UnsupportedFileType {
        file_type: String,
    },
    
    #[error("XML parsing error: {message}")]
    XmlParse {
        message: String,
    },
    
    #[error("File access error: {path}")]
    FileAccess {
        path: String,
    },
}

/// Template parsing errors
#[derive(Debug, thiserror::Error)]
pub enum TemplateParseError {
    #[error("Invalid delimiter at line {line}, column {column}: {message}")]
    InvalidDelimiter {
        line: usize,
        column: usize,
        message: String,
    },
    
    #[error("Unclosed tag '{tag}' at line {line}, column {column}")]
    UnclosedTag {
        tag: String,
        line: usize,
        column: usize,
    },
    
    #[error("Unexpected tag '{tag}' at line {line}, column {column}")]
    UnexpectedTag {
        tag: String,
        line: usize,
        column: usize,
    },
    
    #[error("Invalid regex pattern: {pattern}")]
    InvalidRegex {
        pattern: String,
    },
    
    #[error("Syntax error at line {line}, column {column}: {message}")]
    SyntaxError {
        line: usize,
        column: usize,
        message: String,
    },
}

/// Rendering errors
#[derive(Debug, thiserror::Error)]
pub enum RenderError {
    #[error("Data not found: {path}")]
    DataNotFound {
        path: String,
    },
    
    #[error("Type mismatch for '{field}': expected {expected}, got {actual}")]
    TypeMismatch {
        field: String,
        expected: String,
        actual: String,
    },
    
    #[error("Module error in '{module}': {message}")]
    ModuleError {
        module: String,
        message: String,
    },
    
    #[error("Serialization error: {message}")]
    Serialization {
        message: String,
    },
    
    #[error("Template execution error: {message}")]
    Execution {
        message: String,
    },
}


