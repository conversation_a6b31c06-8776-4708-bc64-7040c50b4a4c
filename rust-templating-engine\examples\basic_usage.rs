//! Basic usage example for Rust Templating Engine

use rust_templating_engine::{TemplateEngine, EngineConfig};
use serde_json::json;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Rust Templating Engine - Basic Usage Example");
    
    // Create engine configuration
    let config = EngineConfig::builder()
        .memory_limit(32 * 1024 * 1024) // 32MB
        .batch_size(1000)
        .delimiters("{", "}")
        .case_sensitive(false)
        .fail_fast(false)
        .build()?;
    
    println!("✅ Engine configuration created");
    
    // Create template engine
    let engine = TemplateEngine::new(config);
    println!("✅ Template engine initialized");
    
    // Sample data
    let data = json!({
        "name": "John Doe",
        "age": 30,
        "company": "Rust Corp",
        "skills": ["Rust", "JavaScript", "Python"],
        "address": {
            "street": "123 Main St",
            "city": "San Francisco",
            "country": "USA"
        },
        "projects": [
            {
                "name": "Project Alpha",
                "status": "completed",
                "budget": 50000
            },
            {
                "name": "Project Beta", 
                "status": "in-progress",
                "budget": 75000
            }
        ]
    });
    
    println!("✅ Sample data prepared");
    
    // Test basic template processing
    let template_content = r#"
Hello {name}!

You are {age} years old and work at {company}.

Your address:
{address.street}
{address.city}, {address.country}

Skills: {skills}
"#;
    
    println!("📝 Processing template content...");
    
    match engine.process_content(template_content, &data).await {
        Ok(result) => {
            println!("✅ Template processed successfully!");
            println!("\n📄 Result:");
            println!("{}", result);
        }
        Err(e) => {
            println!("❌ Error processing template: {}", e);
        }
    }
    
    // Test configuration presets
    println!("\n🔧 Testing configuration presets...");
    
    let dev_config = EngineConfig::development()?;
    println!("✅ Development config: memory_limit = {} MB", 
             dev_config.memory_limit / (1024 * 1024));
    
    let prod_config = EngineConfig::production()?;
    println!("✅ Production config: memory_limit = {} MB", 
             prod_config.memory_limit / (1024 * 1024));
    
    let low_mem_config = EngineConfig::low_memory()?;
    println!("✅ Low memory config: memory_limit = {} MB", 
             low_mem_config.memory_limit / (1024 * 1024));
    
    // Test memory monitoring
    println!("\n📊 Memory monitoring example...");
    let memory_stats = rust_templating_engine::utils_public::get_memory_stats();
    println!("Current memory usage: {} bytes", memory_stats.current);
    println!("Peak memory usage: {} bytes", memory_stats.peak);
    println!("Memory limit: {} bytes", memory_stats.limit);
    
    // Test error handling
    println!("\n🚨 Testing error handling...");
    
    let invalid_template = "Hello {unclosed_placeholder";
    match engine.process_content(invalid_template, &data).await {
        Ok(_) => println!("⚠️  Expected error but got success"),
        Err(e) => println!("✅ Error handling works: {}", e),
    }
    
    println!("\n🎉 All tests completed!");
    
    Ok(())
}
