# Rust Templating Engine - Design Summary

## 🎯 **<PERSON><PERSON><PERSON> tiêu thiết kế**

Dựa trên phân tích docxtemplater, tôi đã lập kế hoạch thiết kế một templating engine bằng Rust với những cải tiến đáng kể:

### **Cải tiến chính:**
1. **Memory Efficiency**: Streaming processing thay vì load toàn bộ document
2. **Type Safety**: Leverage Rust's type system cho compile-time guarantees  
3. **Performance**: Regex compilation một lần, async I/O, parallel processing
4. **Error Handling**: Result<T, E> pattern thay vì exceptions
5. **Modularity**: Trait-based module system cho extensibility
6. **Concurrency**: Async/await support cho I/O operations

## 🏗️ **Architecture Overview**

### **Core Components:**
```
TemplateEngine (Main)
├── DocumentLoader (ZIP + XML parsing)
├── TemplateProcessor (Lexer + Parser + Modules)
├── RenderEngine (Data resolution + Content rendering)
└── OutputBuilder (ZIP creation + XML serialization)
```

### **Key Traits:**
- **Module**: Extensible processing modules
- **DataSource**: Flexible data input
- **Renderer**: Content rendering strategies
- **ErrorHandler**: Error processing và recovery

## 📊 **So sánh với Docxtemplater**

| Aspect | Docxtemplater | Rust Engine |
|--------|---------------|-------------|
| **Memory** | Load toàn bộ document | Streaming processing |
| **Parsing** | String search O(n*m) | Regex O(n) after compilation |
| **Concurrency** | Single-threaded | Async/parallel processing |
| **Error Handling** | Collect-then-throw | Result<T, E> pattern |
| **Type Safety** | Runtime errors | Compile-time guarantees |
| **Modularity** | JavaScript modules | Rust traits |

## 🔧 **Core Data Structures**

### **Engine Configuration:**
```rust
pub struct EngineConfig {
    pub memory_limit: usize,        // Memory usage limit
    pub batch_size: usize,          // Processing batch size
    pub delimiters: DelimiterConfig, // Template delimiters
    pub syntax: SyntaxConfig,       // Syntax options
}
```

### **Template Representation:**
```rust
pub struct Placeholder {
    pub content: String,
    pub position: Position,
    pub placeholder_type: PlaceholderType,
}

pub enum PlaceholderType {
    Simple(String),                 // {field}
    Loop { tag: String, content: Vec<Token> },     // {#items}...{/items}
    Condition { tag: String, content: Vec<Token> }, // {?condition}...{/condition}
    RawXml(String),                // {@rawXml}
}
```

## ⚡ **Performance Optimizations**

### **1. Memory Management:**
```rust
pub struct StreamingProcessor {
    memory_limit: usize,
    current_usage: AtomicUsize,
    batch_buffer: Mutex<Vec<ProcessingUnit>>,
}
```

### **2. Regex Caching:**
```rust
pub struct RegexCache {
    static_patterns: HashMap<String, Regex>,  // Pre-compiled patterns
    dynamic_cache: LruCache<String, Regex>,   // Runtime cache
}
```

### **3. Async Processing:**
```rust
pub async fn process_templates(&self, templates: Vec<Template>) -> Result<Vec<ProcessedTemplate>> {
    let semaphore = Arc::new(Semaphore::new(num_cpus::get()));
    // Parallel processing với concurrency limit
}
```

## 🚨 **Error Handling Strategy**

### **Error Hierarchy:**
```rust
#[derive(Debug, thiserror::Error)]
pub enum TemplateError {
    #[error("Document loading failed: {source}")]
    DocumentLoad { #[from] source: DocumentLoadError },
    
    #[error("Template parsing failed: {message} at {position}")]
    TemplateParse { message: String, position: Position, context: String },
    
    #[error("Rendering failed: {message}")]
    Render { message: String, placeholder: String, data_path: String },
    
    #[error("Memory limit exceeded: {current} > {limit}")]
    MemoryLimit { current: usize, limit: usize },
}
```

### **Context-aware Error Reporting:**
```rust
pub struct ErrorContext {
    pub file_path: String,
    pub line: usize,
    pub column: usize,
    pub snippet: String,
}
```

## 📋 **Implementation Plan**

### **Phase 1: Core Infrastructure (2-3 weeks)**
- Project setup với Cargo.toml
- Error types và Result patterns
- Memory monitoring system
- Basic document loading

### **Phase 2: Template Engine (3-4 weeks)**
- Lexer implementation với regex
- Parser với AST construction
- Module system với traits
- Template processing pipeline

### **Phase 3: Rendering Engine (2-3 weeks)**
- Data resolution system
- Content rendering logic
- Output generation
- Memory-efficient processing

### **Phase 4: Advanced Features (3-4 weeks)**
- Performance optimizations
- Advanced modules (loops, conditions)
- Error handling enhancement
- Parallel processing

### **Phase 5: Testing & Documentation (2-3 weeks)**
- Comprehensive testing suite
- Performance benchmarks
- API documentation
- Migration guide

## 📦 **Key Dependencies**

### **Core:**
- `tokio` - Async runtime
- `regex` - Pattern matching
- `quick-xml` - XML processing
- `zip` - ZIP file handling
- `thiserror` - Error handling

### **Performance:**
- `lru` - Caching
- `parking_lot` - Synchronization
- `crossbeam` - Concurrency utilities

### **Testing:**
- `criterion` - Benchmarking
- `proptest` - Property testing
- `tempfile` - Test utilities

## 🔄 **Migration Strategy**

### **API Compatibility:**
```rust
// Wrapper cho docxtemplater users
pub struct DocxtemplaterCompat {
    engine: TemplateEngine,
}

impl DocxtemplaterCompat {
    pub async fn load_zip(&mut self, zip_data: &[u8]) -> Result<()>
    pub async fn render(&mut self, data: serde_json::Value) -> Result<()>
    pub async fn get_zip(&self) -> Result<Vec<u8>>
}
```

### **Template Syntax Support:**
- `{field}` - Simple placeholders
- `{#loop}...{/loop}` - Loop constructs
- `{?condition}...{/condition}` - Conditional blocks
- `{@rawXml}` - Raw XML insertion
- `{=[ ]=}` - Dynamic delimiter changes

## 🎯 **Expected Benefits**

### **Performance Improvements:**
- **Memory Usage**: 50-80% reduction với streaming
- **Processing Speed**: 2-3x faster với regex + async
- **Concurrency**: Parallel processing cho multiple files
- **Startup Time**: Faster với pre-compiled patterns

### **Developer Experience:**
- **Type Safety**: Compile-time error detection
- **Error Messages**: Detailed context và suggestions
- **API Design**: Intuitive builder patterns
- **Documentation**: Comprehensive examples

### **Operational Benefits:**
- **Memory Monitoring**: Built-in usage tracking
- **Error Recovery**: Graceful failure handling
- **Debugging**: Rich error context
- **Extensibility**: Plugin system với traits

## 🚀 **Conclusion**

Design này cung cấp một foundation mạnh mẽ cho Rust templating engine với những cải tiến đáng kể:

1. **Architecture**: Modular, extensible, type-safe
2. **Performance**: Memory-efficient, async, parallel
3. **Developer Experience**: Intuitive API, rich errors
4. **Compatibility**: Migration path từ docxtemplater
5. **Future-proof**: Extensible module system

**Timeline**: 12-17 tuần implementation
**Team Size**: 2-3 developers
**Risk Level**: Medium (well-defined requirements)

Rust engine sẽ maintain compatibility với docxtemplater templates trong khi cung cấp performance và safety improvements đáng kể cho production use cases.
