//! Error conversion utilities and helpers

use super::types::*;
use super::context::{Position, ErrorContext};

/// Helper trait for adding context to errors
pub trait ErrorContextExt<T> {
    /// Add context to error
    fn with_context<F>(self, f: F) -> Result<T, TemplateError>
    where
        F: FnOnce() -> ErrorContext;
    
    /// Add simple context message
    fn with_message(self, message: &str) -> Result<T, TemplateError>;
    
    /// Add file context
    fn with_file_context(self, file_path: &str, position: Position) -> Result<T, TemplateError>;
}

impl<T, E> ErrorContextExt<T> for Result<T, E>
where
    E: Into<TemplateError>,
{
    fn with_context<F>(self, _f: F) -> Result<T, TemplateError>
    where
        F: FnOnce() -> ErrorContext,
    {
        self.map_err(|e| {
            let error = e.into();
            // Add context to error if possible
            match &error {
                TemplateError::TemplateParse { source: _ } => {
                    // Could enhance with additional context
                }
                TemplateError::Render { source: _ } => {
                    // Could enhance with additional context
                }
                _ => {}
            }
            error
        })
    }
    
    fn with_message(self, message: &str) -> Result<T, TemplateError> {
        self.map_err(|e| {
            let base_error = e.into();
            TemplateError::Internal {
                message: format!("{}: {}", message, base_error),
            }
        })
    }
    
    fn with_file_context(self, file_path: &str, position: Position) -> Result<T, TemplateError> {
        self.with_context(|| {
            ErrorContext::new(
                file_path.to_string(),
                position,
                String::new(),
                "Error occurred".to_string(),
            )
        })
    }
}

// Note: From<std::io::Error> for TemplateError is already implemented by thiserror

impl From<std::io::Error> for DocumentLoadError {
    fn from(error: std::io::Error) -> Self {
        DocumentLoadError::FileAccess {
            path: format!("IO Error: {}", error),
        }
    }
}

/// Convert serde errors to template errors
impl From<serde_json::Error> for TemplateError {
    fn from(error: serde_json::Error) -> Self {
        TemplateError::Internal {
            message: format!("JSON serialization error: {}", error),
        }
    }
}

/// Convert CSV errors to template errors
impl From<csv::Error> for TemplateError {
    fn from(error: csv::Error) -> Self {
        TemplateError::Internal {
            message: format!("CSV processing error: {}", error),
        }
    }
}

impl From<serde_json::Error> for RenderError {
    fn from(error: serde_json::Error) -> Self {
        RenderError::Serialization {
            message: error.to_string(),
        }
    }
}

/// Helper functions for creating specific errors
pub mod helpers {
    use super::*;
    
    /// Create a template parse error with position
    pub fn parse_error_at(
        position: Position,
        message: &str,
    ) -> TemplateParseError {
        TemplateParseError::SyntaxError {
            line: position.line,
            column: position.column,
            message: message.to_string(),
        }
    }
    
    /// Create an unclosed tag error
    pub fn unclosed_tag_error(
        tag: &str,
        position: Position,
    ) -> TemplateParseError {
        TemplateParseError::UnclosedTag {
            tag: tag.to_string(),
            line: position.line,
            column: position.column,
        }
    }
    
    /// Create a data not found error
    pub fn data_not_found_error(path: &str) -> RenderError {
        RenderError::DataNotFound {
            path: path.to_string(),
        }
    }
    
    /// Create a type mismatch error
    pub fn type_mismatch_error(
        field: &str,
        expected: &str,
        actual: &str,
    ) -> RenderError {
        RenderError::TypeMismatch {
            field: field.to_string(),
            expected: expected.to_string(),
            actual: actual.to_string(),
        }
    }
    
    /// Create a memory limit error
    pub fn memory_limit_error(current: usize, limit: usize) -> TemplateError {
        TemplateError::MemoryLimit { current, limit }
    }
    
    /// Create a configuration error
    pub fn config_error(message: &str) -> TemplateError {
        TemplateError::Configuration {
            message: message.to_string(),
        }
    }
    
    /// Create an invalid format error
    pub fn invalid_format_error(message: &str) -> TemplateError {
        TemplateError::InvalidFormat {
            message: message.to_string(),
        }
    }
}

/// Error collection for gathering multiple errors
#[derive(Debug, Default)]
pub struct ErrorCollector {
    errors: Vec<TemplateError>,
    max_errors: usize,
}

impl ErrorCollector {
    /// Create new error collector
    pub fn new(max_errors: usize) -> Self {
        Self {
            errors: Vec::new(),
            max_errors,
        }
    }
    
    /// Add error to collection
    pub fn add_error(&mut self, error: TemplateError) -> bool {
        if self.errors.len() < self.max_errors {
            self.errors.push(error);
            true
        } else {
            false
        }
    }
    
    /// Check if has errors
    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }
    
    /// Get error count
    pub fn error_count(&self) -> usize {
        self.errors.len()
    }
    
    /// Check if at capacity
    pub fn is_full(&self) -> bool {
        self.errors.len() >= self.max_errors
    }
    
    /// Convert to result
    pub fn into_result<T>(self, value: T) -> Result<T, Vec<TemplateError>> {
        if self.errors.is_empty() {
            Ok(value)
        } else {
            Err(self.errors)
        }
    }
    
    /// Get errors
    pub fn errors(&self) -> &[TemplateError] {
        &self.errors
    }
    
    /// Take errors
    pub fn take_errors(&mut self) -> Vec<TemplateError> {
        std::mem::take(&mut self.errors)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_error_context_ext() {
        let result: Result<(), std::io::Error> = Err(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            "File not found",
        ));
        
        let with_message = result.with_message("Failed to read file");
        assert!(with_message.is_err());
        
        let error_msg = format!("{}", with_message.unwrap_err());
        assert!(error_msg.contains("Failed to read file"));
    }
    
    #[test]
    fn test_error_helpers() {
        let parse_error = helpers::parse_error_at(
            Position::new(5, 10, 50),
            "Invalid syntax",
        );
        
        match parse_error {
            TemplateParseError::SyntaxError { line, column, message } => {
                assert_eq!(line, 5);
                assert_eq!(column, 10);
                assert_eq!(message, "Invalid syntax");
            }
            _ => panic!("Wrong error type"),
        }
    }
    
    #[test]
    fn test_error_collector() {
        let mut collector = ErrorCollector::new(2);
        
        assert!(!collector.has_errors());
        assert_eq!(collector.error_count(), 0);
        
        let error1 = TemplateError::InvalidFormat {
            message: "Error 1".to_string(),
        };
        let error2 = TemplateError::InvalidFormat {
            message: "Error 2".to_string(),
        };
        let error3 = TemplateError::InvalidFormat {
            message: "Error 3".to_string(),
        };
        
        assert!(collector.add_error(error1));
        assert!(collector.add_error(error2));
        assert!(!collector.add_error(error3)); // Should be rejected (at capacity)
        
        assert!(collector.has_errors());
        assert_eq!(collector.error_count(), 2);
        assert!(collector.is_full());
        
        let result = collector.into_result(42);
        assert!(result.is_err());
        assert_eq!(result.unwrap_err().len(), 2);
    }
}
