# Rust Templating Engine Design Plan

## Executive Summary

Thiết kế một templating engine bằng Rust dựa trên phân tích docxtemplater, với những cải tiến về performance, memory efficiency, và type safety. Engine sẽ hỗ trợ Office documents (docx, pptx, xlsx) với architecture modular và extensible.

## Design Goals

### Primary Goals
1. **Memory Efficiency**: Streaming processing thay vì load toàn bộ document
2. **Type Safety**: Leverage Rust's type system cho compile-time guarantees
3. **Performance**: Regex compilation một lần, async I/O, zero-copy khi có thể
4. **Error Handling**: Result<T, E> pattern thay vì exceptions
5. **Modularity**: Trait-based module system cho extensibility
6. **Concurrency**: Async/await support cho I/O operations

### Secondary Goals
- Backward compatibility với docxtemplater templates
- Plugin system cho custom functionality
- Comprehensive error messages với context
- Memory usage monitoring và limits

## Architecture Overview

```mermaid
graph TB
    A[TemplateEngine] --> B[DocumentLoader]
    A --> C[TemplateProcessor]
    A --> D[RenderEngine]
    A --> E[OutputBuilder]
    
    B --> F[ZipReader]
    B --> G[XmlParser]
    
    C --> H[Lexer]
    C --> I[Parser]
    C --> J[ModuleManager]
    
    D --> K[DataResolver]
    D --> L[ContentRenderer]
    
    E --> M[ZipWriter]
    E --> N[XmlSerializer]
    
    subgraph "Core Traits"
        O[Module]
        P[Renderer]
        Q[DataSource]
        R[ErrorHandler]
    end
```

## Core Module Structure

### 1. Project Structure
```
rust-templating-engine/
├── Cargo.toml
├── src/
│   ├── lib.rs                 // Public API
│   ├── engine.rs              // Main TemplateEngine
│   ├── document/
│   │   ├── mod.rs
│   │   ├── loader.rs          // DocumentLoader
│   │   ├── zip_reader.rs      // ZIP file handling
│   │   └── xml_parser.rs      // XML parsing
│   ├── template/
│   │   ├── mod.rs
│   │   ├── lexer.rs           // Delimiter parsing
│   │   ├── parser.rs          // Template processing
│   │   └── processor.rs       // TemplateProcessor
│   ├── render/
│   │   ├── mod.rs
│   │   ├── engine.rs          // RenderEngine
│   │   ├── resolver.rs        // DataResolver
│   │   └── renderer.rs        // ContentRenderer
│   ├── modules/
│   │   ├── mod.rs
│   │   ├── manager.rs         // ModuleManager
│   │   ├── common.rs          // CommonModule
│   │   └── traits.rs          // Module traits
│   ├── output/
│   │   ├── mod.rs
│   │   ├── builder.rs         // OutputBuilder
│   │   ├── zip_writer.rs      // ZIP creation
│   │   └── xml_serializer.rs  // XML serialization
│   ├── error/
│   │   ├── mod.rs
│   │   └── types.rs           // Error types
│   └── utils/
│       ├── mod.rs
│       ├── memory.rs          // Memory management
│       └── async_utils.rs     // Async utilities
├── tests/
├── benches/
└── examples/
```

### 2. Core Data Structures

```rust
// Core engine configuration
#[derive(Debug, Clone)]
pub struct EngineConfig {
    pub memory_limit: usize,
    pub batch_size: usize,
    pub delimiters: DelimiterConfig,
    pub syntax: SyntaxConfig,
    pub error_handling: ErrorHandlingConfig,
}

// Delimiter configuration
#[derive(Debug, Clone)]
pub struct DelimiterConfig {
    pub start: String,
    pub end: String,
    pub change_prefix: Option<String>,
}

// Template placeholder representation
#[derive(Debug, Clone)]
pub struct Placeholder {
    pub content: String,
    pub position: Position,
    pub placeholder_type: PlaceholderType,
}

#[derive(Debug, Clone)]
pub enum PlaceholderType {
    Simple(String),
    Loop { tag: String, content: Vec<Token> },
    Condition { tag: String, content: Vec<Token> },
    RawXml(String),
}

// Document representation
#[derive(Debug)]
pub struct Document {
    pub files: HashMap<String, DocumentFile>,
    pub content_types: ContentTypes,
    pub relationships: Relationships,
}

#[derive(Debug)]
pub struct DocumentFile {
    pub path: String,
    pub content: FileContent,
    pub file_type: FileType,
}

#[derive(Debug)]
pub enum FileContent {
    Xml(XmlDocument),
    Binary(Vec<u8>),
    Text(String),
}
```

### 3. Core Traits

```rust
// Module trait for extensibility
pub trait Module: Send + Sync {
    fn name(&self) -> &str;
    fn matchers(&self) -> Vec<Matcher>;
    fn parse(&self, content: &str, context: &ParseContext) -> Result<ParseResult>;
    fn render(&self, placeholder: &Placeholder, data: &DataContext) -> Result<RenderResult>;
    fn post_process(&self, content: &str, context: &PostProcessContext) -> Result<String>;
}

// Data source trait
pub trait DataSource: Send + Sync {
    async fn get_value(&self, path: &str) -> Result<Value>;
    async fn get_array(&self, path: &str) -> Result<Vec<Value>>;
    fn supports_async(&self) -> bool { true }
}

// Renderer trait
pub trait Renderer: Send + Sync {
    async fn render(&self, template: &Template, data: &dyn DataSource) -> Result<String>;
    fn supports_streaming(&self) -> bool { false }
}

// Error handler trait
pub trait ErrorHandler: Send + Sync {
    fn handle_error(&self, error: &TemplateError) -> ErrorAction;
    fn transform_error(&self, error: TemplateError) -> TemplateError { error }
}
```

## Error Handling Strategy

### 1. Error Types Hierarchy

```rust
#[derive(Debug, thiserror::Error)]
pub enum TemplateError {
    #[error("Document loading failed: {source}")]
    DocumentLoad {
        #[from]
        source: DocumentLoadError,
    },
    
    #[error("Template parsing failed: {message} at {position}")]
    TemplateParse {
        message: String,
        position: Position,
        context: String,
    },
    
    #[error("Rendering failed: {message}")]
    Render {
        message: String,
        placeholder: String,
        data_path: String,
    },
    
    #[error("Memory limit exceeded: {current} > {limit}")]
    MemoryLimit {
        current: usize,
        limit: usize,
    },
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("XML parsing error: {0}")]
    Xml(#[from] quick_xml::Error),
}

#[derive(Debug, thiserror::Error)]
pub enum DocumentLoadError {
    #[error("Invalid ZIP file")]
    InvalidZip,
    #[error("Missing required file: {file}")]
    MissingFile { file: String },
    #[error("Unsupported file type")]
    UnsupportedFileType,
}
```

### 2. Result Types

```rust
pub type Result<T> = std::result::Result<T, TemplateError>;
pub type DocumentResult<T> = std::result::Result<T, DocumentLoadError>;

// Context-aware error reporting
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub file_path: String,
    pub line: usize,
    pub column: usize,
    pub snippet: String,
}
```

## Memory Management Strategy

### 1. Streaming Architecture

```rust
// Memory-conscious document processing
pub struct StreamingProcessor {
    memory_limit: usize,
    current_usage: AtomicUsize,
    batch_buffer: Mutex<Vec<ProcessingUnit>>,
}

impl StreamingProcessor {
    pub async fn process_document(&self, document: Document) -> Result<ProcessedDocument> {
        let mut stream = document.into_stream();
        let mut output = ProcessedDocument::new();
        
        while let Some(chunk) = stream.next().await {
            if self.would_exceed_memory_limit(&chunk) {
                self.flush_batch().await?;
            }
            
            let processed = self.process_chunk(chunk).await?;
            output.add_chunk(processed);
        }
        
        self.flush_batch().await?;
        Ok(output)
    }
    
    fn would_exceed_memory_limit(&self, chunk: &ProcessingUnit) -> bool {
        let current = self.current_usage.load(Ordering::Relaxed);
        let chunk_size = chunk.memory_size();
        current + chunk_size > self.memory_limit
    }
}
```

### 2. Memory Monitoring

```rust
// Memory usage tracking
#[derive(Debug)]
pub struct MemoryMonitor {
    limit: usize,
    current: AtomicUsize,
    peak: AtomicUsize,
    allocations: AtomicUsize,
}

impl MemoryMonitor {
    pub fn allocate(&self, size: usize) -> Result<()> {
        let current = self.current.fetch_add(size, Ordering::Relaxed) + size;
        
        if current > self.limit {
            self.current.fetch_sub(size, Ordering::Relaxed);
            return Err(TemplateError::MemoryLimit {
                current,
                limit: self.limit,
            });
        }
        
        // Update peak usage
        self.peak.fetch_max(current, Ordering::Relaxed);
        self.allocations.fetch_add(1, Ordering::Relaxed);
        
        Ok(())
    }
    
    pub fn deallocate(&self, size: usize) {
        self.current.fetch_sub(size, Ordering::Relaxed);
    }
}
```

## Performance Optimizations

### 1. Regex Compilation Strategy

```rust
// Pre-compiled regex patterns
pub struct RegexCache {
    static_patterns: HashMap<String, Regex>,
    dynamic_cache: LruCache<String, Regex>,
    cache_mutex: Mutex<()>,
}

impl RegexCache {
    pub fn new() -> Self {
        let mut static_patterns = HashMap::new();
        
        // Pre-compile common patterns
        static_patterns.insert(
            "simple_placeholder".to_string(),
            Regex::new(r"\{([^}]+)\}").unwrap()
        );
        static_patterns.insert(
            "loop_start".to_string(),
            Regex::new(r"\{#([^}]+)\}").unwrap()
        );
        static_patterns.insert(
            "loop_end".to_string(),
            Regex::new(r"\{/([^}]+)\}").unwrap()
        );
        
        Self {
            static_patterns,
            dynamic_cache: LruCache::new(100),
            cache_mutex: Mutex::new(()),
        }
    }
    
    pub fn get_or_compile(&self, pattern: &str) -> Result<&Regex> {
        if let Some(regex) = self.static_patterns.get(pattern) {
            return Ok(regex);
        }
        
        let _lock = self.cache_mutex.lock().unwrap();
        if let Some(regex) = self.dynamic_cache.get(pattern) {
            return Ok(regex);
        }
        
        let regex = Regex::new(pattern)
            .map_err(|e| TemplateError::TemplateParse {
                message: format!("Invalid regex pattern: {}", e),
                position: Position::default(),
                context: pattern.to_string(),
            })?;
        
        self.dynamic_cache.put(pattern.to_string(), regex);
        Ok(self.dynamic_cache.get(pattern).unwrap())
    }
}
```

### 2. Async Processing

```rust
// Async template processing
pub struct AsyncTemplateProcessor {
    regex_cache: Arc<RegexCache>,
    module_manager: Arc<ModuleManager>,
    memory_monitor: Arc<MemoryMonitor>,
}

impl AsyncTemplateProcessor {
    pub async fn process_templates(&self, templates: Vec<Template>) -> Result<Vec<ProcessedTemplate>> {
        let semaphore = Arc::new(Semaphore::new(num_cpus::get()));
        let tasks: Vec<_> = templates
            .into_iter()
            .map(|template| {
                let processor = self.clone();
                let permit = semaphore.clone();
                
                tokio::spawn(async move {
                    let _permit = permit.acquire().await.unwrap();
                    processor.process_single_template(template).await
                })
            })
            .collect();
        
        let results = futures::future::try_join_all(tasks).await?;
        results.into_iter().collect()
    }
    
    async fn process_single_template(&self, template: Template) -> Result<ProcessedTemplate> {
        // Process template with memory monitoring
        let _memory_guard = self.memory_monitor.allocate(template.memory_size())?;
        
        // Parse placeholders
        let placeholders = self.parse_placeholders(&template.content).await?;
        
        // Process with modules
        let processed_placeholders = self.process_with_modules(placeholders).await?;
        
        Ok(ProcessedTemplate {
            original: template,
            placeholders: processed_placeholders,
        })
    }
}
```

## API Design

### 1. Main Engine API

```rust
// Main template engine
pub struct TemplateEngine {
    config: EngineConfig,
    modules: Arc<ModuleManager>,
    regex_cache: Arc<RegexCache>,
    memory_monitor: Arc<MemoryMonitor>,
}

impl TemplateEngine {
    pub fn new(config: EngineConfig) -> Self {
        Self {
            modules: Arc::new(ModuleManager::new()),
            regex_cache: Arc::new(RegexCache::new()),
            memory_monitor: Arc::new(MemoryMonitor::new(config.memory_limit)),
            config,
        }
    }

    pub fn add_module<M: Module + 'static>(&mut self, module: M) {
        self.modules.add_module(Box::new(module));
    }

    pub async fn load_document<P: AsRef<Path>>(&self, path: P) -> Result<Document> {
        let loader = DocumentLoader::new(&self.config);
        loader.load(path).await
    }

    pub async fn render_document(
        &self,
        document: Document,
        data: &dyn DataSource,
    ) -> Result<Document> {
        let processor = TemplateProcessor::new(
            self.regex_cache.clone(),
            self.modules.clone(),
        );

        let render_engine = RenderEngine::new(
            self.memory_monitor.clone(),
        );

        let processed = processor.process(document).await?;
        render_engine.render(processed, data).await
    }

    pub async fn save_document<P: AsRef<Path>>(
        &self,
        document: Document,
        path: P,
    ) -> Result<()> {
        let builder = OutputBuilder::new(&self.config);
        builder.save(document, path).await
    }

    // Convenience method for full pipeline
    pub async fn process_template<P1, P2>(
        &self,
        input_path: P1,
        output_path: P2,
        data: &dyn DataSource,
    ) -> Result<()>
    where
        P1: AsRef<Path>,
        P2: AsRef<Path>,
    {
        let document = self.load_document(input_path).await?;
        let rendered = self.render_document(document, data).await?;
        self.save_document(rendered, output_path).await
    }
}
```

### 2. Builder Pattern for Configuration

```rust
// Configuration builder
pub struct EngineConfigBuilder {
    memory_limit: usize,
    batch_size: usize,
    delimiters: DelimiterConfig,
    syntax: SyntaxConfig,
    error_handling: ErrorHandlingConfig,
}

impl EngineConfigBuilder {
    pub fn new() -> Self {
        Self {
            memory_limit: 64 * 1024 * 1024, // 64MB default
            batch_size: 1000,
            delimiters: DelimiterConfig::default(),
            syntax: SyntaxConfig::default(),
            error_handling: ErrorHandlingConfig::default(),
        }
    }

    pub fn memory_limit(mut self, limit: usize) -> Self {
        self.memory_limit = limit;
        self
    }

    pub fn batch_size(mut self, size: usize) -> Self {
        self.batch_size = size;
        self
    }

    pub fn delimiters(mut self, start: &str, end: &str) -> Self {
        self.delimiters = DelimiterConfig {
            start: start.to_string(),
            end: end.to_string(),
            change_prefix: None,
        };
        self
    }

    pub fn build(self) -> EngineConfig {
        EngineConfig {
            memory_limit: self.memory_limit,
            batch_size: self.batch_size,
            delimiters: self.delimiters,
            syntax: self.syntax,
            error_handling: self.error_handling,
        }
    }
}
```

## Implementation Plan

### Phase 1: Core Infrastructure (2-3 weeks)
1. **Project Setup**
   - Cargo.toml với dependencies
   - Basic module structure
   - Error types và Result patterns
   - Memory monitoring system

2. **Document Loading**
   - ZIP file reading với async I/O
   - XML parsing với quick-xml
   - Content type detection
   - File type identification

3. **Basic Template Processing**
   - Regex-based delimiter parsing
   - Simple placeholder extraction
   - Memory-conscious processing

### Phase 2: Template Engine (3-4 weeks)
1. **Lexer Implementation**
   - Delimiter detection với regex
   - Token generation
   - Position tracking cho error reporting

2. **Parser Implementation**
   - Template AST construction
   - Placeholder type detection
   - Nested structure handling

3. **Module System**
   - Trait definitions
   - Module manager
   - Common module implementation

### Phase 3: Rendering Engine (2-3 weeks)
1. **Data Resolution**
   - DataSource trait implementation
   - Async value resolution
   - Scope management

2. **Content Rendering**
   - Template rendering với data
   - Loop và condition handling
   - Raw XML insertion

3. **Output Generation**
   - XML serialization
   - ZIP file creation
   - Memory-efficient output

### Phase 4: Advanced Features (3-4 weeks)
1. **Performance Optimizations**
   - Regex caching
   - Parallel processing
   - Memory pooling

2. **Advanced Modules**
   - Loop module
   - Condition module
   - Custom function support

3. **Error Handling Enhancement**
   - Detailed error context
   - Error recovery strategies
   - Debugging support

### Phase 5: Testing & Documentation (2-3 weeks)
1. **Comprehensive Testing**
   - Unit tests cho tất cả modules
   - Integration tests
   - Performance benchmarks
   - Memory leak testing

2. **Documentation**
   - API documentation
   - Usage examples
   - Migration guide từ docxtemplater
```
