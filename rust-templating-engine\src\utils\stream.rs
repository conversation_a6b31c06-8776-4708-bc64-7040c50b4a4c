//! Stream utilities for async processing

use std::pin::Pin;
use std::task::{Context, Poll};
use std::collections::VecDeque;

/// Simple async stream trait
pub trait AsyncStream {
    type Item;
    
    /// Poll for next item
    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>>;
}

/// Buffered stream for batching items
pub struct BufferedStream<T> {
    buffer: VecDeque<T>,
    batch_size: usize,
    is_closed: bool,
}

impl<T> BufferedStream<T> {
    /// Create new buffered stream
    pub fn new(batch_size: usize) -> Self {
        Self {
            buffer: VecDeque::new(),
            batch_size,
            is_closed: false,
        }
    }
    
    /// Add item to buffer
    pub fn push(&mut self, item: T) {
        if !self.is_closed {
            self.buffer.push_back(item);
        }
    }
    
    /// Add multiple items to buffer
    pub fn push_batch(&mut self, items: Vec<T>) {
        if !self.is_closed {
            self.buffer.extend(items);
        }
    }
    
    /// Close the stream
    pub fn close(&mut self) {
        self.is_closed = true;
    }
    
    /// Check if stream is closed
    pub fn is_closed(&self) -> bool {
        self.is_closed
    }
    
    /// Get buffer length
    pub fn len(&self) -> usize {
        self.buffer.len()
    }
    
    /// Check if buffer is empty
    pub fn is_empty(&self) -> bool {
        self.buffer.is_empty()
    }
    
    /// Take next batch
    pub fn take_batch(&mut self) -> Option<Vec<T>> {
        if self.buffer.is_empty() {
            if self.is_closed {
                None
            } else {
                Some(Vec::new())
            }
        } else {
            let count = std::cmp::min(self.batch_size, self.buffer.len());
            let batch = self.buffer.drain(..count).collect();
            Some(batch)
        }
    }
    
    /// Take all remaining items
    pub fn take_all(&mut self) -> Vec<T> {
        self.buffer.drain(..).collect()
    }
}

impl<T> AsyncStream for BufferedStream<T> {
    type Item = Vec<T>;
    
    fn poll_next(mut self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        if let Some(batch) = self.take_batch() {
            if batch.is_empty() && !self.is_closed {
                Poll::Pending
            } else {
                Poll::Ready(Some(batch))
            }
        } else {
            Poll::Ready(None)
        }
    }
}

/// Chunk stream for processing data in chunks
pub struct ChunkStream {
    data: Vec<u8>,
    chunk_size: usize,
    position: usize,
}

impl ChunkStream {
    /// Create new chunk stream
    pub fn new(data: Vec<u8>, chunk_size: usize) -> Self {
        Self {
            data,
            chunk_size,
            position: 0,
        }
    }
    
    /// Get next chunk
    pub fn next_chunk(&mut self) -> Option<&[u8]> {
        if self.position >= self.data.len() {
            None
        } else {
            let end = std::cmp::min(self.position + self.chunk_size, self.data.len());
            let chunk = &self.data[self.position..end];
            self.position = end;
            Some(chunk)
        }
    }
    
    /// Get remaining data length
    pub fn remaining(&self) -> usize {
        self.data.len().saturating_sub(self.position)
    }
    
    /// Check if stream is finished
    pub fn is_finished(&self) -> bool {
        self.position >= self.data.len()
    }
    
    /// Reset stream to beginning
    pub fn reset(&mut self) {
        self.position = 0;
    }
}

impl AsyncStream for ChunkStream {
    type Item = Vec<u8>;
    
    fn poll_next(mut self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        if let Some(chunk) = self.next_chunk() {
            Poll::Ready(Some(chunk.to_vec()))
        } else {
            Poll::Ready(None)
        }
    }
}

/// Stream combinator utilities
pub mod combinators {
    use super::*;
    
    /// Map stream items
    pub struct MapStream<S, F> {
        stream: S,
        mapper: F,
    }
    
    impl<S, F, T, U> MapStream<S, F>
    where
        S: AsyncStream<Item = T>,
        F: FnMut(T) -> U,
    {
        pub fn new(stream: S, mapper: F) -> Self {
            Self { stream, mapper }
        }
    }
    
    impl<S, F, T, U> AsyncStream for MapStream<S, F>
    where
        S: AsyncStream<Item = T>,
        F: FnMut(T) -> U,
    {
        type Item = U;
        
        fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
            // Safety: We're not moving the stream
            let stream = unsafe { self.as_mut().map_unchecked_mut(|s| &mut s.stream) };
            
            match Pin::new(stream).poll_next(cx) {
                Poll::Ready(Some(item)) => {
                    let mapped = (self.mapper)(item);
                    Poll::Ready(Some(mapped))
                }
                Poll::Ready(None) => Poll::Ready(None),
                Poll::Pending => Poll::Pending,
            }
        }
    }
    
    /// Filter stream items
    pub struct FilterStream<S, F> {
        stream: S,
        filter: F,
    }
    
    impl<S, F, T> FilterStream<S, F>
    where
        S: AsyncStream<Item = T>,
        F: FnMut(&T) -> bool,
    {
        pub fn new(stream: S, filter: F) -> Self {
            Self { stream, filter }
        }
    }
    
    impl<S, F, T> AsyncStream for FilterStream<S, F>
    where
        S: AsyncStream<Item = T>,
        F: FnMut(&T) -> bool,
    {
        type Item = T;
        
        fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
            loop {
                // Safety: We're not moving the stream
                let stream = unsafe { self.as_mut().map_unchecked_mut(|s| &mut s.stream) };
                
                match Pin::new(stream).poll_next(cx) {
                    Poll::Ready(Some(item)) => {
                        if (self.filter)(&item) {
                            return Poll::Ready(Some(item));
                        }
                        // Continue to next item
                    }
                    Poll::Ready(None) => return Poll::Ready(None),
                    Poll::Pending => return Poll::Pending,
                }
            }
        }
    }
    
    /// Take first N items from stream
    pub struct TakeStream<S> {
        stream: S,
        remaining: usize,
    }
    
    impl<S> TakeStream<S> {
        pub fn new(stream: S, count: usize) -> Self {
            Self {
                stream,
                remaining: count,
            }
        }
    }
    
    impl<S> AsyncStream for TakeStream<S>
    where
        S: AsyncStream,
    {
        type Item = S::Item;
        
        fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
            if self.remaining == 0 {
                return Poll::Ready(None);
            }
            
            // Safety: We're not moving the stream
            let stream = unsafe { self.as_mut().map_unchecked_mut(|s| &mut s.stream) };
            
            match Pin::new(stream).poll_next(cx) {
                Poll::Ready(Some(item)) => {
                    self.remaining -= 1;
                    Poll::Ready(Some(item))
                }
                Poll::Ready(None) => Poll::Ready(None),
                Poll::Pending => Poll::Pending,
            }
        }
    }
}

/// Stream extension trait
pub trait StreamExt: AsyncStream + Sized {
    /// Map stream items
    fn map<F, U>(self, mapper: F) -> combinators::MapStream<Self, F>
    where
        F: FnMut(Self::Item) -> U,
    {
        combinators::MapStream::new(self, mapper)
    }
    
    /// Filter stream items
    fn filter<F>(self, filter: F) -> combinators::FilterStream<Self, F>
    where
        F: FnMut(&Self::Item) -> bool,
    {
        combinators::FilterStream::new(self, filter)
    }
    
    /// Take first N items
    fn take(self, count: usize) -> combinators::TakeStream<Self> {
        combinators::TakeStream::new(self, count)
    }
}

impl<S: AsyncStream> StreamExt for S {}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_buffered_stream() {
        let mut stream = BufferedStream::new(3);
        
        // Add items
        stream.push(1);
        stream.push(2);
        stream.push(3);
        stream.push(4);
        
        // Take batch
        let batch = stream.take_batch().unwrap();
        assert_eq!(batch, vec![1, 2, 3]);
        
        // Remaining item
        let batch = stream.take_batch().unwrap();
        assert_eq!(batch, vec![4]);
        
        // Close stream
        stream.close();
        assert!(stream.is_closed());
        
        // No more items
        assert!(stream.take_batch().is_none());
    }
    
    #[test]
    fn test_chunk_stream() {
        let data = b"hello world".to_vec();
        let mut stream = ChunkStream::new(data, 3);
        
        assert_eq!(stream.next_chunk(), Some(b"hel".as_slice()));
        assert_eq!(stream.next_chunk(), Some(b"lo ".as_slice()));
        assert_eq!(stream.next_chunk(), Some(b"wor".as_slice()));
        assert_eq!(stream.next_chunk(), Some(b"ld".as_slice()));
        assert_eq!(stream.next_chunk(), None);
        
        assert!(stream.is_finished());
        
        // Reset and try again
        stream.reset();
        assert!(!stream.is_finished());
        assert_eq!(stream.next_chunk(), Some(b"hel".as_slice()));
    }
}
