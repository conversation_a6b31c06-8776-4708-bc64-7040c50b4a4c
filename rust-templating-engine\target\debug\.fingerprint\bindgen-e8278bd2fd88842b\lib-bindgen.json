{"rustc": 12200708597866198150, "features": "[\"default\", \"logging\", \"prettyplease\", \"runtime\", \"which-rustfmt\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_16\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\", \"which-rustfmt\"]", "target": 15460903241111225995, "profile": 2225463790103693989, "path": 6734711424710598315, "deps": [[950716570147248582, "cexpr", false, 10274399256254069863], [2004958070545769120, "lazycell", false, 12688569379327922232], [3060637413840920116, "proc_macro2", false, 6090349418290637220], [4885725550624711673, "clang_sys", false, 15230350838462629184], [4974441333307933176, "syn", false, 18361986414640281086], [5986029879202738730, "log", false, 17247417012456187523], [6243494903393190189, "which", false, 12861911356166532047], [7896293946984509699, "bitflags", false, 13891028335121853008], [8410525223747752176, "shlex", false, 8068157026520117574], [9451456094439810778, "regex", false, 4004789130242508549], [9907446631595856440, "build_script_build", false, 15562013758396791194], [13211303279345757084, "prettyplease", false, 11272273339741010124], [14931062873021150766, "itertools", false, 1835258108845970396], [16055916053474393816, "rustc_hash", false, 16689040471120989557], [17917672826516349275, "lazy_static", false, 2823777315946587417], [17990358020177143287, "quote", false, 6430127862117674003]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bindgen-e8278bd2fd88842b\\dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}