//! Memory management utilities

use std::sync::atomic::{AtomicUsize, Ordering};
use crate::error::{Result, TemplateError};

/// Memory statistics
#[derive(Debug, Default, <PERSON>lone)]
pub struct MemoryStats {
    pub current: usize,
    pub peak: usize,
    pub limit: usize,
    pub allocations: usize,
}

impl MemoryStats {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn usage_ratio(&self) -> f64 {
        if self.limit == 0 {
            0.0
        } else {
            self.current as f64 / self.limit as f64
        }
    }
}

/// Memory monitor with atomic counters
#[derive(Debug)]
pub struct MemoryMonitor {
    current_usage: AtomicUsize,
    peak_usage: AtomicUsize,
    limit: usize,
    allocations: AtomicUsize,
}

impl MemoryMonitor {
    /// Create new memory monitor
    pub fn new(limit: usize) -> Self {
        Self {
            current_usage: AtomicUsize::new(0),
            peak_usage: AtomicUsize::new(0),
            limit,
            allocations: AtomicUsize::new(0),
        }
    }

    /// Allocate memory with guard
    pub fn allocate(&self, size: usize) -> Result<MemoryGuard> {
        let new_usage = self.current_usage.fetch_add(size, Ordering::Relaxed) + size;

        if new_usage > self.limit {
            self.current_usage.fetch_sub(size, Ordering::Relaxed);
            return Err(TemplateError::MemoryLimit {
                current: new_usage,
                limit: self.limit,
            });
        }

        // Update peak usage
        self.peak_usage.fetch_max(new_usage, Ordering::Relaxed);
        self.allocations.fetch_add(1, Ordering::Relaxed);

        Ok(MemoryGuard {
            monitor: self,
            size,
        })
    }

    /// Deallocate memory
    fn deallocate(&self, size: usize) {
        self.current_usage.fetch_sub(size, Ordering::Relaxed);
    }

    /// Get current memory usage
    pub fn current_usage(&self) -> usize {
        self.current_usage.load(Ordering::Relaxed)
    }

    /// Get peak memory usage
    pub fn peak_usage(&self) -> usize {
        self.peak_usage.load(Ordering::Relaxed)
    }

    /// Get memory limit
    pub fn limit(&self) -> usize {
        self.limit
    }

    /// Get usage ratio (0.0 to 1.0+)
    pub fn usage_ratio(&self) -> f64 {
        if self.limit == 0 {
            0.0
        } else {
            self.current_usage() as f64 / self.limit as f64
        }
    }

    /// Get memory statistics
    pub fn stats(&self) -> MemoryStats {
        MemoryStats {
            current: self.current_usage(),
            peak: self.peak_usage(),
            limit: self.limit,
            allocations: self.allocations.load(Ordering::Relaxed),
        }
    }

    /// Reset peak usage
    pub fn reset_peak(&self) {
        let current = self.current_usage();
        self.peak_usage.store(current, Ordering::Relaxed);
    }
}

/// RAII memory guard
pub struct MemoryGuard<'a> {
    monitor: &'a MemoryMonitor,
    size: usize,
}

impl<'a> Drop for MemoryGuard<'a> {
    fn drop(&mut self) {
        self.monitor.deallocate(self.size);
    }
}

impl<'a> MemoryGuard<'a> {
    /// Get allocated size
    pub fn size(&self) -> usize {
        self.size
    }
}
