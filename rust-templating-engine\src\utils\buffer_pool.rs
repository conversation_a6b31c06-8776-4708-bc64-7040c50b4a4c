//! Buffer pooling for memory efficiency

use std::sync::Mutex;
use std::collections::VecDeque;

/// Buffer pool for reusing memory allocations
#[derive(Debug)]
pub struct BufferPool<T> {
    buffers: Mutex<VecDeque<T>>,
    max_size: usize,
    create_fn: fn() -> T,
    reset_fn: fn(&mut T),
}

impl<T> BufferPool<T> {
    /// Create new buffer pool
    pub fn new(max_size: usize, create_fn: fn() -> T, reset_fn: fn(&mut T)) -> Self {
        Self {
            buffers: Mutex::new(VecDeque::new()),
            max_size,
            create_fn,
            reset_fn,
        }
    }
    
    /// Get buffer from pool or create new one
    pub fn get(&self) -> T {
        let mut buffers = self.buffers.lock().unwrap();
        buffers.pop_front().unwrap_or_else(|| (self.create_fn)())
    }
    
    /// Return buffer to pool
    pub fn put(&self, mut buffer: T) {
        (self.reset_fn)(&mut buffer);
        
        let mut buffers = self.buffers.lock().unwrap();
        if buffers.len() < self.max_size {
            buffers.push_back(buffer);
        }
        // If pool is full, buffer is dropped
    }
    
    /// Get pool size
    pub fn size(&self) -> usize {
        self.buffers.lock().unwrap().len()
    }
    
    /// Clear pool
    pub fn clear(&self) {
        self.buffers.lock().unwrap().clear();
    }
}

/// String buffer pool
pub type StringBufferPool = BufferPool<String>;

impl StringBufferPool {
    /// Create string buffer pool
    pub fn new_string_pool(max_size: usize) -> Self {
        Self::new(
            max_size,
            || String::with_capacity(1024),
            |s| s.clear(),
        )
    }
}

/// Vec<u8> buffer pool
pub type ByteBufferPool = BufferPool<Vec<u8>>;

impl ByteBufferPool {
    /// Create byte buffer pool
    pub fn new_byte_pool(max_size: usize) -> Self {
        Self::new(
            max_size,
            || Vec::with_capacity(1024),
            |v| v.clear(),
        )
    }
}

/// RAII buffer guard for automatic return to pool
pub struct PooledBuffer<T> {
    buffer: Option<T>,
    pool: std::sync::Arc<BufferPool<T>>,
}

impl<T> PooledBuffer<T> {
    /// Create new pooled buffer
    pub fn new(pool: std::sync::Arc<BufferPool<T>>) -> Self {
        let buffer = pool.get();
        Self {
            buffer: Some(buffer),
            pool,
        }
    }
    
    /// Get mutable reference to buffer
    pub fn get_mut(&mut self) -> &mut T {
        self.buffer.as_mut().unwrap()
    }
    
    /// Get reference to buffer
    pub fn get(&self) -> &T {
        self.buffer.as_ref().unwrap()
    }
    
    /// Take buffer out of guard (won't be returned to pool)
    pub fn take(mut self) -> T {
        self.buffer.take().unwrap()
    }
}

impl<T> Drop for PooledBuffer<T> {
    fn drop(&mut self) {
        if let Some(buffer) = self.buffer.take() {
            self.pool.put(buffer);
        }
    }
}

impl<T> std::ops::Deref for PooledBuffer<T> {
    type Target = T;
    
    fn deref(&self) -> &Self::Target {
        self.get()
    }
}

impl<T> std::ops::DerefMut for PooledBuffer<T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        self.get_mut()
    }
}

/// Global buffer pools
pub struct GlobalBufferPools {
    string_pool: std::sync::Arc<StringBufferPool>,
    byte_pool: std::sync::Arc<ByteBufferPool>,
}

impl GlobalBufferPools {
    /// Create global buffer pools
    pub fn new(max_size: usize) -> Self {
        Self {
            string_pool: std::sync::Arc::new(StringBufferPool::new_string_pool(max_size)),
            byte_pool: std::sync::Arc::new(ByteBufferPool::new_byte_pool(max_size)),
        }
    }
    
    /// Get string buffer
    pub fn get_string_buffer(&self) -> PooledBuffer<String> {
        PooledBuffer::new(self.string_pool.clone())
    }
    
    /// Get byte buffer
    pub fn get_byte_buffer(&self) -> PooledBuffer<Vec<u8>> {
        PooledBuffer::new(self.byte_pool.clone())
    }
    
    /// Get pool statistics
    pub fn stats(&self) -> BufferPoolStats {
        BufferPoolStats {
            string_pool_size: self.string_pool.size(),
            byte_pool_size: self.byte_pool.size(),
        }
    }
    
    /// Clear all pools
    pub fn clear_all(&self) {
        self.string_pool.clear();
        self.byte_pool.clear();
    }
}

/// Buffer pool statistics
#[derive(Debug, Clone)]
pub struct BufferPoolStats {
    pub string_pool_size: usize,
    pub byte_pool_size: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_buffer_pool() {
        let pool = StringBufferPool::new_string_pool(2);
        
        // Get buffer
        let mut buf1 = pool.get();
        buf1.push_str("test");
        
        // Return buffer
        pool.put(buf1);
        assert_eq!(pool.size(), 1);
        
        // Get buffer again (should be reused and cleared)
        let buf2 = pool.get();
        assert!(buf2.is_empty());
        assert_eq!(pool.size(), 0);
    }
    
    #[test]
    fn test_pooled_buffer() {
        let pool = std::sync::Arc::new(StringBufferPool::new_string_pool(2));
        
        {
            let mut buffer = PooledBuffer::new(pool.clone());
            buffer.push_str("test");
            assert_eq!(buffer.as_str(), "test");
        } // Buffer automatically returned to pool
        
        assert_eq!(pool.size(), 1);
        
        // Get buffer again
        let buffer = pool.get();
        assert!(buffer.is_empty()); // Should be cleared
    }
    
    #[test]
    fn test_global_pools() {
        let pools = GlobalBufferPools::new(5);
        
        {
            let mut string_buf = pools.get_string_buffer();
            string_buf.push_str("hello");
            
            let mut byte_buf = pools.get_byte_buffer();
            byte_buf.extend_from_slice(b"world");
        }
        
        let stats = pools.stats();
        assert_eq!(stats.string_pool_size, 1);
        assert_eq!(stats.byte_pool_size, 1);
    }
}
