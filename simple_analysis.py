#!/usr/bin/env python3
"""
Phân tích đơn giản file CSV và XLSX
"""

import pandas as pd
import json
import os

def analyze_csv_simple():
    print("=== PHÂN TÍCH FILE CSV ===")
    try:
        # Đọc file CSV
        df = pd.read_csv("D:/InsertData/dulieu.csv", encoding='utf-8')
        
        print(f"Số hàng: {len(df)}")
        print(f"Số cột: {len(df.columns)}")
        print(f"Tên cột: {list(df.columns)}")
        print(f"Kích thước file: {os.path.getsize('D:/InsertData/dulieu.csv')} bytes")
        
        # Lấy nội dung cột đầu tiên
        if len(df) > 0:
            content = df.iloc[0, 0]
            print(f"Kiểu dữ liệu: {type(content)}")
            print(f"Độ dài nội dung: {len(str(content))}")
            
            # Thử parse JSON
            try:
                if isinstance(content, str):
                    # Tìm JSON trong string
                    start_idx = content.find('[{')
                    if start_idx != -1:
                        json_part = content[start_idx:]
                        # Tìm end của JSON array
                        bracket_count = 0
                        end_idx = -1
                        for i, char in enumerate(json_part):
                            if char == '[':
                                bracket_count += 1
                            elif char == ']':
                                bracket_count -= 1
                                if bracket_count == 0:
                                    end_idx = i + 1
                                    break
                        
                        if end_idx != -1:
                            json_str = json_part[:end_idx]
                            data = json.loads(json_str)
                            print(f"✅ Tìm thấy JSON data!")
                            print(f"Số records: {len(data)}")
                            if len(data) > 0:
                                print(f"Cấu trúc record đầu tiên:")
                                first_record = data[0]
                                for key, value in first_record.items():
                                    print(f"  - {key}: {type(value).__name__}")
                                
                                print(f"\nVí dụ record đầu tiên:")
                                print(json.dumps(first_record, indent=2, ensure_ascii=False))
                                
                                # Kiểm tra chữ "i"
                                count_with_i = 0
                                for record in data:
                                    for key, value in record.items():
                                        if isinstance(value, str) and 'i' in value.lower():
                                            count_with_i += 1
                                            break
                                print(f"\nSố records chứa chữ 'i': {count_with_i}")
                        else:
                            print("❌ Không tìm thấy JSON hợp lệ")
                    else:
                        print("❌ Không tìm thấy JSON trong nội dung")
            except Exception as e:
                print(f"❌ Lỗi parse JSON: {e}")
                
    except Exception as e:
        print(f"❌ Lỗi đọc CSV: {e}")

def analyze_xlsx_simple():
    print("\n=== PHÂN TÍCH FILE XLSX ===")
    try:
        # Đọc file XLSX
        excel_file = pd.ExcelFile("D:/InsertData/MNF (1).xlsx")
        print(f"Số sheets: {len(excel_file.sheet_names)}")
        print(f"Tên sheets: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- Sheet: {sheet_name} ---")
            df = pd.read_excel("D:/InsertData/MNF (1).xlsx", sheet_name=sheet_name)
            print(f"Số hàng: {len(df)}")
            print(f"Số cột: {len(df.columns)}")
            print(f"Tên cột: {list(df.columns)}")
            
            if len(df) > 0:
                print("Mẫu dữ liệu (3 hàng đầu):")
                print(df.head(3).to_string())
                
                # Kiểm tra chữ "i"
                count_with_i = 0
                for col in df.columns:
                    if df[col].dtype == 'object':
                        contains_i = df[col].astype(str).str.contains('i', case=False, na=False)
                        count_with_i += contains_i.sum()
                
                print(f"Tổng số cell chứa chữ 'i': {count_with_i}")
                
    except Exception as e:
        print(f"❌ Lỗi đọc XLSX: {e}")

def main():
    print("🔍 PHÂN TÍCH DỮ LIỆU ĐơN GIẢN")
    print("=" * 50)
    
    analyze_csv_simple()
    analyze_xlsx_simple()
    
    print("\n🚀 KẾT LUẬN VỀ TÍCH HỢP VỚI RUST TEMPLATING ENGINE:")
    print("1. File CSV chứa dữ liệu JSON - có thể dùng làm data source")
    print("2. File XLSX chứa dữ liệu structured - có thể convert thành JSON")
    print("3. Cả hai file đều có thể tích hợp với JsonDataSource trong engine")
    print("4. Có thể tạo templates với placeholders tương ứng với các fields")

if __name__ == "__main__":
    main()
