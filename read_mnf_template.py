#!/usr/bin/env python3
"""
Đọc file MNF (1).xlsx để hiểu cấu trúc template
"""

import pandas as pd
import openpyxl
import os

def analyze_mnf_template():
    """Phân tích cấu trúc file MNF template"""
    mnf_path = "D:/InsertData/MNF (1).xlsx"
    
    if not os.path.exists(mnf_path):
        print("❌ File MNF (1).xlsx không tồn tại")
        return
    
    print("📁 Analyzing MNF template structure...")
    
    try:
        # Đọc với openpyxl để xem cấu trúc chi tiết
        wb = openpyxl.load_workbook(mnf_path)
        
        print(f"📊 Workbook có {len(wb.sheetnames)} sheets:")
        for i, sheet_name in enumerate(wb.sheetnames):
            print(f"  {i+1}. {sheet_name}")
        
        # Phân tích sheet đầu tiên
        ws = wb.active
        print(f"\n📋 Active sheet: {ws.title}")
        print(f"📏 Dimensions: {ws.max_row} rows x {ws.max_column} columns")
        
        print("\n📄 Content preview (first 20 rows):")
        for row in range(1, min(21, ws.max_row + 1)):
            row_data = []
            for col in range(1, min(11, ws.max_column + 1)):  # First 10 columns
                cell = ws.cell(row=row, column=col)
                value = cell.value
                if value is not None:
                    if isinstance(value, str) and len(value) > 30:
                        value = value[:30] + "..."
                    row_data.append(f"{value}")
                else:
                    row_data.append("")
            
            if any(row_data):  # Only print non-empty rows
                print(f"Row {row:2d}: {' | '.join(row_data)}")
        
        # Tìm các cell có placeholder patterns
        print("\n🔍 Looking for placeholder patterns...")
        placeholder_patterns = ["{", "}", "[", "]", "{{", "}}", "<<", ">>"]
        found_placeholders = []
        
        for row in range(1, ws.max_row + 1):
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=row, column=col)
                if cell.value and isinstance(cell.value, str):
                    for pattern in placeholder_patterns:
                        if pattern in cell.value:
                            found_placeholders.append({
                                'row': row,
                                'col': col,
                                'value': cell.value,
                                'address': cell.coordinate
                            })
                            break
        
        if found_placeholders:
            print("📍 Found potential placeholders:")
            for placeholder in found_placeholders[:10]:  # Show first 10
                print(f"  {placeholder['address']}: {placeholder['value']}")
        else:
            print("❌ No obvious placeholders found")
        
        # Tìm các cell có text patterns phổ biến
        print("\n🔍 Looking for common field patterns...")
        field_patterns = ["bill", "receiver", "shipper", "weight", "date", "created", "name", "address"]
        found_fields = []
        
        for row in range(1, ws.max_row + 1):
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=row, column=col)
                if cell.value and isinstance(cell.value, str):
                    cell_lower = cell.value.lower()
                    for pattern in field_patterns:
                        if pattern in cell_lower:
                            found_fields.append({
                                'row': row,
                                'col': col,
                                'value': cell.value,
                                'address': cell.coordinate,
                                'pattern': pattern
                            })
                            break
        
        if found_fields:
            print("📍 Found potential field locations:")
            for field in found_fields[:15]:  # Show first 15
                print(f"  {field['address']}: {field['value']} (pattern: {field['pattern']})")
        
        # Phân tích merged cells
        print(f"\n🔗 Merged cells: {len(ws.merged_cells.ranges)}")
        for merged_range in list(ws.merged_cells.ranges)[:5]:  # Show first 5
            print(f"  {merged_range}")
        
    except Exception as e:
        print(f"❌ Error reading MNF file: {e}")

if __name__ == "__main__":
    analyze_mnf_template()
