const { resolveSoon } = require("../utils.js");

describe("Nullgetter", () => {
	it("should call nullgetter for loops synchonously", function () {
		return this.render({
			name: "multi-loop.docx",
			data: {
				test2: "Value2",
			},
			options: {
				paragraphLoop: true,
				nullGetter(part) {
					if (part.module === "loop") {
						return [
							{
								name: "<PERSON>c<PERSON>",
								users: [
									{
										name: "<PERSON>",
									},
									{
										name: "<PERSON>",
									},
								],
							},
							{
								name: "<PERSON><PERSON>",
								users: [
									{
										name: "<PERSON>",
									},
									{
										name: "<PERSON>",
									},
								],
							},
						];
					}
				},
			},
			expectedName: "expected-multi-loop.docx",
		});
	});

	it("should call nullgetter for loops async", function () {
		return this.render({
			name: "multi-loop.docx",
			data: {
				test2: "Value2",
			},
			options: {
				paragraphLoop: true,
				nullGetter(part) {
					if (part.module === "loop") {
						return resolveSoon([
							{
								name: "<PERSON>c<PERSON>",
								users: resolveSoon(
									[
										{
											name: resolve<PERSON><PERSON>("<PERSON>", 25),
										},
										resolveSoon({
											name: "<PERSON>",
										}),
									],
									5
								),
							},
							resolveSoon(
								{
									name: resolve<PERSON><PERSON>("<PERSON><PERSON>"),
									users: resolve<PERSON>oon([
										{
											name: "<PERSON>",
										},
										{
											name: "<PERSON>",
										},
									]),
								},
								20
							),
						]);
					}
				},
				async: true,
			},
			expectedName: "expected-multi-loop.docx",
			async: true,
		});
	});

	it("should call nullGetter with empty rawxml", function () {
		return this.renderV4({
			name: "table-raw-xml.docx",
			options: {
				nullGetter: (part) => {
					if (part.module === "rawxml") {
						return `<w:p>
                        <w:r>
                            <w:rPr><w:color w:val="FF0000"/></w:rPr>
                            <w:t>UNDEFINED</w:t>
                        </w:r>
                        </w:p>`;
					}
				},
			},
			expectedName: "expected-raw-xml-null.docx",
		});
	});
});
