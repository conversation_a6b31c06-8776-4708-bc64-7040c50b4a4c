//! Configuration builder with advanced validation

use super::*;
use crate::error::{Result, TemplateError};

/// Advanced configuration builder
impl EngineConfigBuilder {
    /// Set memory limit with validation
    pub fn memory_limit(mut self, limit: usize) -> Self {
        self.memory_limit = limit;
        self
    }
    
    /// Set batch size with validation
    pub fn batch_size(mut self, size: usize) -> Self {
        self.batch_size = size;
        self
    }
    
    /// Set delimiters with validation
    pub fn delimiters(mut self, start: &str, end: &str) -> Self {
        self.delimiters = DelimiterConfig {
            start: start.to_string(),
            end: end.to_string(),
            change_prefix: Some("=".to_string()),
        };
        self
    }
    
    /// Set delimiter change prefix
    pub fn delimiter_change_prefix(mut self, prefix: Option<&str>) -> Self {
        self.delimiters.change_prefix = prefix.map(|s| s.to_string());
        self
    }
    
    /// Configure syntax settings
    pub fn syntax(mut self, config: SyntaxConfig) -> Self {
        self.syntax = config;
        self
    }
    
    /// Set loop prefixes
    pub fn loop_prefixes(mut self, start: &str, end: &str) -> Self {
        self.syntax.loop_start_prefix = start.to_string();
        self.syntax.loop_end_prefix = end.to_string();
        self
    }
    
    /// Set condition prefix
    pub fn condition_prefix(mut self, prefix: &str) -> Self {
        self.syntax.condition_prefix = prefix.to_string();
        self
    }
    
    /// Set raw XML prefix
    pub fn raw_xml_prefix(mut self, prefix: &str) -> Self {
        self.syntax.raw_xml_prefix = prefix.to_string();
        self
    }
    
    /// Set case sensitivity
    pub fn case_sensitive(mut self, sensitive: bool) -> Self {
        self.syntax.case_sensitive = sensitive;
        self
    }
    
    /// Configure error handling
    pub fn error_handling(mut self, config: ErrorHandlingConfig) -> Self {
        self.error_handling = config;
        self
    }
    
    /// Set fail fast mode
    pub fn fail_fast(mut self, fail_fast: bool) -> Self {
        self.error_handling.fail_fast = fail_fast;
        self
    }
    
    /// Set error collection mode
    pub fn collect_all_errors(mut self, collect: bool) -> Self {
        self.error_handling.collect_all_errors = collect;
        self
    }
    
    /// Set maximum errors to collect
    pub fn max_errors(mut self, max: usize) -> Self {
        self.error_handling.max_errors = max;
        self
    }
    
    /// Include context in errors
    pub fn include_context(mut self, include: bool) -> Self {
        self.error_handling.include_context = include;
        self
    }
    
    /// Configure performance settings
    pub fn performance(mut self, config: PerformanceConfig) -> Self {
        self.performance = config;
        self
    }
    
    /// Set regex cache size
    pub fn regex_cache_size(mut self, size: usize) -> Self {
        self.performance.regex_cache_size = size;
        self
    }
    
    /// Set buffer pool size
    pub fn buffer_pool_size(mut self, size: usize) -> Self {
        self.performance.buffer_pool_size = size;
        self
    }
    
    /// Set maximum concurrent tasks
    pub fn max_concurrent_tasks(mut self, max: usize) -> Self {
        self.performance.max_concurrent_tasks = max;
        self
    }
    
    /// Set I/O timeout
    pub fn io_timeout(mut self, timeout: Duration) -> Self {
        self.performance.io_timeout = timeout;
        self
    }
    
    /// Set backpressure threshold
    pub fn backpressure_threshold(mut self, threshold: f64) -> Self {
        self.performance.backpressure_threshold = threshold.clamp(0.0, 1.0);
        self
    }
    
    /// Build configuration with comprehensive validation
    pub fn build(self) -> Result<EngineConfig> {
        self.validate()?;
        
        Ok(EngineConfig {
            memory_limit: self.memory_limit,
            batch_size: self.batch_size,
            delimiters: self.delimiters,
            syntax: self.syntax,
            error_handling: self.error_handling,
            performance: self.performance,
        })
    }
    
    /// Validate configuration
    fn validate(&self) -> Result<()> {
        // Memory validation
        if self.memory_limit == 0 {
            return Err(TemplateError::Configuration {
                message: "Memory limit cannot be zero".to_string(),
            });
        }
        
        if self.memory_limit < 1024 * 1024 {
            return Err(TemplateError::Configuration {
                message: "Memory limit should be at least 1MB".to_string(),
            });
        }
        
        // Batch size validation
        if self.batch_size == 0 {
            return Err(TemplateError::Configuration {
                message: "Batch size cannot be zero".to_string(),
            });
        }
        
        if self.batch_size > 100_000 {
            return Err(TemplateError::Configuration {
                message: "Batch size too large (max 100,000)".to_string(),
            });
        }
        
        // Delimiter validation
        if self.delimiters.start.is_empty() || self.delimiters.end.is_empty() {
            return Err(TemplateError::Configuration {
                message: "Delimiters cannot be empty".to_string(),
            });
        }
        
        if self.delimiters.start == self.delimiters.end {
            return Err(TemplateError::Configuration {
                message: "Start and end delimiters must be different".to_string(),
            });
        }
        
        if self.delimiters.start.len() > 10 || self.delimiters.end.len() > 10 {
            return Err(TemplateError::Configuration {
                message: "Delimiters too long (max 10 characters)".to_string(),
            });
        }
        
        // Syntax validation
        if self.syntax.loop_start_prefix.is_empty() || self.syntax.loop_end_prefix.is_empty() {
            return Err(TemplateError::Configuration {
                message: "Loop prefixes cannot be empty".to_string(),
            });
        }
        
        if self.syntax.condition_prefix.is_empty() {
            return Err(TemplateError::Configuration {
                message: "Condition prefix cannot be empty".to_string(),
            });
        }
        
        // Error handling validation
        if self.error_handling.max_errors == 0 {
            return Err(TemplateError::Configuration {
                message: "Max errors cannot be zero".to_string(),
            });
        }
        
        if self.error_handling.max_errors > 10_000 {
            return Err(TemplateError::Configuration {
                message: "Max errors too large (max 10,000)".to_string(),
            });
        }
        
        // Performance validation
        if self.performance.regex_cache_size == 0 {
            return Err(TemplateError::Configuration {
                message: "Regex cache size cannot be zero".to_string(),
            });
        }
        
        if self.performance.buffer_pool_size == 0 {
            return Err(TemplateError::Configuration {
                message: "Buffer pool size cannot be zero".to_string(),
            });
        }
        
        if self.performance.max_concurrent_tasks == 0 {
            return Err(TemplateError::Configuration {
                message: "Max concurrent tasks cannot be zero".to_string(),
            });
        }
        
        if self.performance.max_concurrent_tasks > 1000 {
            return Err(TemplateError::Configuration {
                message: "Too many concurrent tasks (max 1000)".to_string(),
            });
        }
        
        if self.performance.backpressure_threshold < 0.1 || self.performance.backpressure_threshold > 1.0 {
            return Err(TemplateError::Configuration {
                message: "Backpressure threshold must be between 0.1 and 1.0".to_string(),
            });
        }
        
        Ok(())
    }
}

/// Configuration presets for common use cases
impl EngineConfig {
    /// Configuration for low memory environments
    pub fn low_memory() -> Result<Self> {
        EngineConfigBuilder::new()
            .memory_limit(16 * 1024 * 1024) // 16MB
            .batch_size(100)
            .regex_cache_size(20)
            .buffer_pool_size(10)
            .max_concurrent_tasks(2)
            .backpressure_threshold(0.6)
            .build()
    }
    
    /// Configuration for high performance
    pub fn high_performance() -> Result<Self> {
        EngineConfigBuilder::new()
            .memory_limit(256 * 1024 * 1024) // 256MB
            .batch_size(5000)
            .regex_cache_size(500)
            .buffer_pool_size(200)
            .max_concurrent_tasks(num_cpus() * 2)
            .backpressure_threshold(0.9)
            .build()
    }
    
    /// Configuration for development/testing
    pub fn development() -> Result<Self> {
        EngineConfigBuilder::new()
            .memory_limit(32 * 1024 * 1024) // 32MB
            .batch_size(500)
            .fail_fast(true)
            .collect_all_errors(true)
            .include_context(true)
            .max_errors(50)
            .build()
    }
    
    /// Configuration for production
    pub fn production() -> Result<Self> {
        EngineConfigBuilder::new()
            .memory_limit(128 * 1024 * 1024) // 128MB
            .batch_size(2000)
            .fail_fast(false)
            .collect_all_errors(false)
            .include_context(false)
            .max_errors(10)
            .regex_cache_size(200)
            .buffer_pool_size(100)
            .build()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_builder_validation() {
        // Valid configuration
        let config = EngineConfigBuilder::new()
            .memory_limit(32 * 1024 * 1024)
            .batch_size(1000)
            .build();
        assert!(config.is_ok());
        
        // Invalid memory limit
        let config = EngineConfigBuilder::new()
            .memory_limit(0)
            .build();
        assert!(config.is_err());
        
        // Invalid batch size
        let config = EngineConfigBuilder::new()
            .batch_size(0)
            .build();
        assert!(config.is_err());
        
        // Invalid delimiters
        let config = EngineConfigBuilder::new()
            .delimiters("", "}")
            .build();
        assert!(config.is_err());
        
        // Same delimiters
        let config = EngineConfigBuilder::new()
            .delimiters("{", "{")
            .build();
        assert!(config.is_err());
    }
    
    #[test]
    fn test_presets() {
        assert!(EngineConfig::low_memory().is_ok());
        assert!(EngineConfig::high_performance().is_ok());
        assert!(EngineConfig::development().is_ok());
        assert!(EngineConfig::production().is_ok());
    }
    
    #[test]
    fn test_builder_fluent_api() {
        let config = EngineConfigBuilder::new()
            .memory_limit(64 * 1024 * 1024)
            .batch_size(2000)
            .delimiters("{{", "}}")
            .case_sensitive(true)
            .fail_fast(false)
            .regex_cache_size(100)
            .build()
            .unwrap();
        
        assert_eq!(config.memory_limit, 64 * 1024 * 1024);
        assert_eq!(config.batch_size, 2000);
        assert_eq!(config.delimiters.start, "{{");
        assert_eq!(config.delimiters.end, "}}");
        assert!(config.syntax.case_sensitive);
        assert!(!config.error_handling.fail_fast);
        assert_eq!(config.performance.regex_cache_size, 100);
    }
}
