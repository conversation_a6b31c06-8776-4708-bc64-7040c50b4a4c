//! Memory pressure detection and handling

use super::memory::MemoryMonitor;
use std::time::{Duration, Instant};

/// Memory pressure levels
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, PartialOrd, Ord)]
pub enum MemoryPressure {
    Low,      // < 50% usage
    Medium,   // 50-70% usage
    High,     // 70-90% usage
    Critical, // > 90% usage
}

impl MemoryPressure {
    /// Get pressure level from usage ratio
    pub fn from_ratio(ratio: f64) -> Self {
        match ratio {
            r if r < 0.5 => MemoryPressure::Low,
            r if r < 0.7 => MemoryPressure::Medium,
            r if r < 0.9 => MemoryPressure::High,
            _ => MemoryPressure::Critical,
        }
    }
    
    /// Get recommended delay for this pressure level
    pub fn recommended_delay(&self) -> Duration {
        match self {
            MemoryPressure::Low => Duration::from_millis(0),
            MemoryPressure::Medium => Duration::from_millis(1),
            MemoryPressure::High => Duration::from_millis(10),
            MemoryPressure::Critical => Duration::from_millis(100),
        }
    }
    
    /// Check if should trigger cleanup
    pub fn should_cleanup(&self) -> bool {
        matches!(self, MemoryPressure::High | MemoryPressure::Critical)
    }
    
    /// Check if should reject new allocations
    pub fn should_reject(&self) -> bool {
        matches!(self, MemoryPressure::Critical)
    }
}

impl std::fmt::Display for MemoryPressure {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MemoryPressure::Low => write!(f, "LOW"),
            MemoryPressure::Medium => write!(f, "MEDIUM"),
            MemoryPressure::High => write!(f, "HIGH"),
            MemoryPressure::Critical => write!(f, "CRITICAL"),
        }
    }
}

/// Memory pressure detector
#[derive(Debug)]
pub struct PressureDetector {
    monitor: std::sync::Arc<MemoryMonitor>,
    last_check: std::sync::Mutex<Instant>,
    check_interval: Duration,
    history: std::sync::Mutex<Vec<(Instant, MemoryPressure)>>,
    max_history: usize,
}

impl PressureDetector {
    /// Create new pressure detector
    pub fn new(monitor: std::sync::Arc<MemoryMonitor>) -> Self {
        Self {
            monitor,
            last_check: std::sync::Mutex::new(Instant::now()),
            check_interval: Duration::from_millis(100),
            history: std::sync::Mutex::new(Vec::new()),
            max_history: 100,
        }
    }
    
    /// Get current memory pressure
    pub fn current_pressure(&self) -> MemoryPressure {
        let ratio = self.monitor.usage_ratio();
        let pressure = MemoryPressure::from_ratio(ratio);
        
        // Update history if enough time has passed
        let now = Instant::now();
        let mut last_check = self.last_check.lock().unwrap();
        if now.duration_since(*last_check) >= self.check_interval {
            *last_check = now;
            
            let mut history = self.history.lock().unwrap();
            history.push((now, pressure));
            
            // Keep history size bounded
            if history.len() > self.max_history {
                history.remove(0);
            }
        }
        
        pressure
    }
    
    /// Get pressure trend over time
    pub fn pressure_trend(&self) -> PressureTrend {
        let history = self.history.lock().unwrap();
        
        if history.len() < 2 {
            return PressureTrend::Stable;
        }
        
        let recent_count = std::cmp::min(10, history.len());
        let recent = &history[history.len() - recent_count..];
        
        let first_pressure = recent[0].1 as u8;
        let last_pressure = recent[recent.len() - 1].1 as u8;
        
        match last_pressure.cmp(&first_pressure) {
            std::cmp::Ordering::Greater => PressureTrend::Increasing,
            std::cmp::Ordering::Less => PressureTrend::Decreasing,
            std::cmp::Ordering::Equal => PressureTrend::Stable,
        }
    }
    
    /// Get pressure statistics
    pub fn pressure_stats(&self) -> PressureStats {
        let history = self.history.lock().unwrap();
        let current = self.current_pressure();
        
        if history.is_empty() {
            return PressureStats {
                current,
                trend: PressureTrend::Stable,
                avg_pressure: current as u8 as f64,
                time_in_critical: Duration::ZERO,
                time_in_high: Duration::ZERO,
            };
        }
        
        let trend = self.pressure_trend();
        let avg_pressure = history.iter()
            .map(|(_, p)| *p as u8 as f64)
            .sum::<f64>() / history.len() as f64;
        
        // Calculate time spent in high/critical pressure
        let mut time_in_critical = Duration::ZERO;
        let mut time_in_high = Duration::ZERO;
        
        for window in history.windows(2) {
            let duration = window[1].0.duration_since(window[0].0);
            match window[0].1 {
                MemoryPressure::Critical => time_in_critical += duration,
                MemoryPressure::High => time_in_high += duration,
                _ => {}
            }
        }
        
        PressureStats {
            current,
            trend,
            avg_pressure,
            time_in_critical,
            time_in_high,
        }
    }
    
    /// Clear pressure history
    pub fn clear_history(&self) {
        self.history.lock().unwrap().clear();
    }
}

/// Pressure trend
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PressureTrend {
    Increasing,
    Decreasing,
    Stable,
}

impl std::fmt::Display for PressureTrend {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PressureTrend::Increasing => write!(f, "INCREASING"),
            PressureTrend::Decreasing => write!(f, "DECREASING"),
            PressureTrend::Stable => write!(f, "STABLE"),
        }
    }
}

/// Pressure statistics
#[derive(Debug, Clone)]
pub struct PressureStats {
    pub current: MemoryPressure,
    pub trend: PressureTrend,
    pub avg_pressure: f64,
    pub time_in_critical: Duration,
    pub time_in_high: Duration,
}

impl std::fmt::Display for PressureStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "Memory Pressure Stats:")?;
        writeln!(f, "  Current: {}", self.current)?;
        writeln!(f, "  Trend: {}", self.trend)?;
        writeln!(f, "  Average: {:.2}", self.avg_pressure)?;
        writeln!(f, "  Time in HIGH: {:?}", self.time_in_high)?;
        writeln!(f, "  Time in CRITICAL: {:?}", self.time_in_critical)?;
        Ok(())
    }
}

/// Pressure-aware processing helper
pub struct PressureAwareProcessor {
    detector: PressureDetector,
}

impl PressureAwareProcessor {
    /// Create new pressure-aware processor
    pub fn new(monitor: std::sync::Arc<MemoryMonitor>) -> Self {
        Self {
            detector: PressureDetector::new(monitor),
        }
    }
    
    /// Wait based on current pressure
    pub async fn pressure_delay(&self) {
        let pressure = self.detector.current_pressure();
        let delay = pressure.recommended_delay();
        
        if !delay.is_zero() {
            tokio::time::sleep(delay).await;
        }
    }
    
    /// Check if should proceed with operation
    pub fn should_proceed(&self) -> bool {
        let pressure = self.detector.current_pressure();
        !pressure.should_reject()
    }
    
    /// Check if should trigger cleanup
    pub fn should_cleanup(&self) -> bool {
        let pressure = self.detector.current_pressure();
        pressure.should_cleanup()
    }
    
    /// Get pressure detector
    pub fn detector(&self) -> &PressureDetector {
        &self.detector
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::memory::MemoryMonitor;
    
    #[test]
    fn test_memory_pressure_levels() {
        assert_eq!(MemoryPressure::from_ratio(0.3), MemoryPressure::Low);
        assert_eq!(MemoryPressure::from_ratio(0.6), MemoryPressure::Medium);
        assert_eq!(MemoryPressure::from_ratio(0.8), MemoryPressure::High);
        assert_eq!(MemoryPressure::from_ratio(0.95), MemoryPressure::Critical);
    }
    
    #[test]
    fn test_pressure_detector() {
        let monitor = std::sync::Arc::new(MemoryMonitor::new(1000));
        let detector = PressureDetector::new(monitor.clone());
        
        // Initially should be low pressure
        assert_eq!(detector.current_pressure(), MemoryPressure::Low);
        
        // Allocate some memory
        let _guard = monitor.allocate(800).unwrap();
        assert_eq!(detector.current_pressure(), MemoryPressure::High);
    }
    
    #[test]
    fn test_pressure_aware_processor() {
        let monitor = std::sync::Arc::new(MemoryMonitor::new(1000));
        let processor = PressureAwareProcessor::new(monitor.clone());
        
        // Should proceed when pressure is low
        assert!(processor.should_proceed());
        assert!(!processor.should_cleanup());
        
        // Allocate memory to increase pressure
        let _guard = monitor.allocate(950).unwrap();
        
        // Should still proceed but should cleanup
        assert!(processor.should_proceed());
        assert!(processor.should_cleanup());
    }
}
